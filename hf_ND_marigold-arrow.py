import diffusers
import torch
import cv2
import numpy as np

'''
已知原图image和表面法线图normals，normals尺寸为(W,H,3)，其三个通道的取值范围为[-1, 1]
以image为底图，将从image中稀疏采样的10x10个坐标点的法线绘制在image上
比如(0,0,1)的法线指向观察者，故箭头没有长度；(1,0,0)的法线指向右侧，箭头长度为1，(0,1,0)的法线指向上方，箭头长度为1。
0711debug通过
'''
    
imgPath = "Datasets/mushroom_dev/1_20210524173424_color.jpg"

# 计算表面法线图
image = diffusers.utils.load_image(imgPath)
pipe_normal = diffusers.MarigoldNormalsPipeline.from_pretrained(
    "prs-eth/marigold-normals-lcm-v0-1", variant="fp16", torch_dtype=torch.float16, local_files_only=True
).to("cuda")
normals = pipe_normal(image)
vis_normal = pipe_normal.image_processor.visualize_normals(normals.prediction)
vis_normal[0].save("dev-utils/results/marigold_normal_rgb褐菇.png")

# 计算深度图
pipe_depth = diffusers.MarigoldDepthPipeline.from_pretrained(
    "prs-eth/marigold-depth-lcm-v1-0", variant="fp16", torch_dtype=torch.float16, local_files_only=True
).to("cuda")
depth = pipe_depth(image)
vis_depth = pipe_depth.image_processor.visualize_depth(depth.prediction)  # matplotlib’s colormaps (Spectral by default
vis_depth[0].save("dev-utils/results/marigold_depth_rgb褐菇.png")

# 读取原图
image = cv2.imread(imgPath)
normals = normals.prediction[0]

# 从原图中稀疏采样50x50个点，并计算相应位置处法线的终点坐标，记录在列表中
points = []
scale = 30
height, width = image.shape[:2]
step_h = height // 50  # 在高度方向采样50个点
step_w = width // 50   # 在宽度方向采样50个点
for i in range(0, height, step_h):
    for j in range(0, width, step_w):
        # 从法线图中获取法线
        normal = normals[i, j]
        # 计算法线的终点坐标
        x = j
        y = i
        z = 0
        length = np.linalg.norm(normal)
        normal = normal / length
        x_end = x + normal[0] * scale
        y_end = y + normal[1] * scale * (-1)
        z_end = z + normal[2] * scale
        points.append([(x, y, z), (x_end, y_end, z_end)])

# 使cv2的方法，根据points中的坐标把所有的箭头绘制在原图上，箭头本身为红色，起点为绿色，终点为黄色
for point in points:
    # 箭头起点
    cv2.circle(image, (int(point[0][0]), int(point[0][1])), 1, (0, 255, 0), -1)
    # # 箭头终点
    # cv2.circle(image, (int(point[1][0]), int(point[1][1])), 1, (0, 255, 255), -1)
    # 箭头
    cv2.arrowedLine(image, (int(point[0][0]), int(point[0][1])), (int(point[1][0]), int(point[1][1])), (0, 0, 255), 1)
# 将图保存到本地
cv2.imwrite("dev-utils/results/marigold_normal_arrow褐菇.jpg", image)