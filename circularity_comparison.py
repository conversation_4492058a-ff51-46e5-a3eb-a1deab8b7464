#!/usr/bin/env python3
"""
对比不同圆度计算方法的示例
展示为什么使用 4π × 面积 / 周长² 是最合适的
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle
import math

def create_test_shapes():
    """创建测试形状"""
    shapes = {}
    
    # 1. 完美圆形
    img_circle = np.zeros((200, 200), dtype=np.uint8)
    cv2.circle(img_circle, (100, 100), 50, 255, -1)
    shapes['perfect_circle'] = img_circle
    
    # 2. 椭圆
    img_ellipse = np.zeros((200, 200), dtype=np.uint8)
    cv2.ellipse(img_ellipse, (100, 100), (50, 30), 0, 0, 360, 255, -1)
    shapes['ellipse'] = img_ellipse
    
    # 3. 正方形
    img_square = np.zeros((200, 200), dtype=np.uint8)
    cv2.rectangle(img_square, (75, 75), (125, 125), 255, -1)
    shapes['square'] = img_square
    
    # 4. 三角形
    img_triangle = np.zeros((200, 200), dtype=np.uint8)
    pts = np.array([[100, 60], [70, 130], [130, 130]], np.int32)
    cv2.fillPoly(img_triangle, [pts], 255)
    shapes['triangle'] = img_triangle
    
    # 5. 不规则形状（模拟噪声边界）
    img_irregular = np.zeros((200, 200), dtype=np.uint8)
    cv2.circle(img_irregular, (100, 100), 50, 255, -1)
    # 添加一些噪声
    for i in range(10):
        angle = i * 36
        x = int(100 + 45 * math.cos(math.radians(angle)))
        y = int(100 + 45 * math.sin(math.radians(angle)))
        cv2.circle(img_irregular, (x, y), 8, 0, -1)  # 挖掉一些小洞
    shapes['irregular'] = img_irregular
    
    return shapes

def calculate_basic_circularity(mask):
    """计算基础圆度：4π × 面积 / 周长²"""
    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    if not contours:
        return 0.0
    
    max_contour = max(contours, key=cv2.contourArea)
    area = cv2.contourArea(max_contour)
    perimeter = cv2.arcLength(max_contour, True)
    
    if perimeter == 0 or area == 0:
        return 0.0
    
    return 4 * np.pi * area / (perimeter ** 2)

def calculate_fitting_circle_circularity(mask):
    """使用最小外接圆计算圆度"""
    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    if not contours:
        return 0.0, None
    
    max_contour = max(contours, key=cv2.contourArea)
    
    # 最小外接圆
    (x, y), radius = cv2.minEnclosingCircle(max_contour)
    circle_area = np.pi * radius * radius
    shape_area = cv2.contourArea(max_contour)
    
    # 方法1：形状面积 / 外接圆面积
    ratio_circularity = shape_area / circle_area if circle_area > 0 else 0
    
    return ratio_circularity, ((int(x), int(y)), int(radius))

def calculate_least_squares_circle_circularity(mask):
    """使用最小二乘法拟合圆计算圆度"""
    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    if not contours:
        return 0.0, None
    
    max_contour = max(contours, key=cv2.contourArea)
    points = max_contour.reshape(-1, 2).astype(np.float64)
    
    if len(points) < 3:
        return 0.0, None
    
    # 最小二乘法拟合圆
    # 建立方程组 (x-a)² + (y-b)² = r²
    # 展开: x² + y² - 2ax - 2by + (a² + b² - r²) = 0
    # 设 D = -2a, E = -2b, F = a² + b² - r²
    # 则: x² + y² + Dx + Ey + F = 0
    
    n = len(points)
    x = points[:, 0]
    y = points[:, 1]
    
    # 构造矩阵A和向量b
    A = np.column_stack([x, y, np.ones(n)])
    b = -(x**2 + y**2)
    
    try:
        # 求解最小二乘解
        coeffs = np.linalg.lstsq(A, b, rcond=None)[0]
        D, E, F = coeffs
        
        # 计算圆心和半径
        center_x = -D / 2
        center_y = -E / 2
        radius = np.sqrt((D**2 + E**2) / 4 - F)
        
        if radius <= 0:
            return 0.0, None
        
        # 计算拟合误差
        distances = np.sqrt((x - center_x)**2 + (y - center_y)**2)
        mean_distance = np.mean(distances)
        std_distance = np.std(distances)
        
        # 圆度 = 1 - (标准差 / 平均距离)
        fit_circularity = max(0, 1 - std_distance / mean_distance) if mean_distance > 0 else 0
        
        return fit_circularity, ((int(center_x), int(center_y)), int(radius))
    
    except:
        return 0.0, None

def compare_methods():
    """对比不同方法的结果"""
    shapes = create_test_shapes()
    
    print("=" * 80)
    print("圆度计算方法对比")
    print("=" * 80)
    print(f"{'形状':<15} {'基础圆度':<12} {'外接圆比值':<12} {'最小二乘拟合':<15}")
    print("-" * 80)
    
    # 可视化结果
    fig, axes = plt.subplots(2, len(shapes), figsize=(15, 8))
    
    for i, (name, mask) in enumerate(shapes.items()):
        # 计算不同方法的圆度
        basic_circ = calculate_basic_circularity(mask)
        fitting_circ, fitting_circle = calculate_fitting_circle_circularity(mask)
        ls_circ, ls_circle = calculate_least_squares_circle_circularity(mask)
        
        print(f"{name:<15} {basic_circ:<12.4f} {fitting_circ:<12.4f} {ls_circ:<15.4f}")
        
        # 显示原始形状
        axes[0, i].imshow(mask, cmap='gray')
        axes[0, i].set_title(f'{name}\nBasic: {basic_circ:.3f}')
        axes[0, i].axis('off')
        
        # 显示拟合圆对比
        axes[1, i].imshow(mask, cmap='gray', alpha=0.7)
        
        # 绘制外接圆
        if fitting_circle:
            center, radius = fitting_circle
            circle = Circle(center, radius, fill=False, color='red', linewidth=2, label=f'out_circle {fitting_circ:.3f}')
            axes[1, i].add_patch(circle)
        
        # 绘制最小二乘拟合圆
        if ls_circle:
            center, radius = ls_circle
            circle = Circle(center, radius, fill=False, color='blue', linewidth=2, linestyle='--', label=f'least_squares {ls_circ:.3f}')
            axes[1, i].add_patch(circle)
        
        axes[1, i].set_xlim(0, 200)
        axes[1, i].set_ylim(200, 0)
        axes[1, i].set_title('comprison')
        axes[1, i].axis('off')
        if i == 0:
            axes[1, i].legend()
    
    plt.tight_layout()
    plt.savefig('/home/<USER>/dev-utils/mushroom_4th/circularity_comparison.png', dpi=150, bbox_inches='tight')
    print(f"\n可视化结果已保存到: /home/<USER>/dev-utils/mushroom_4th/circularity_comparison.png")
    
    print("\n" + "=" * 80)
    print("分析结论:")
    print("=" * 80)
    print("1. 基础圆度公式 (4π×面积/周长²):")
    print("   - 完美圆形 = 1.0")
    print("   - 其他形状 < 1.0，越不圆越小")
    print("   - 计算简单，无需拟合过程")
    print("   - 直接反映形状的内在几何特性")
    print()
    print("2. 外接圆面积比值:")
    print("   - 容易受到少数离群点影响")
    print("   - 对于有凹陷的形状表现不佳")
    print()
    print("3. 最小二乘拟合圆:")
    print("   - 计算复杂，需要求解线性方程组")
    print("   - 对噪声敏感")
    print("   - 拟合结果可能不稳定")

if __name__ == "__main__":
    compare_methods()
