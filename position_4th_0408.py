import numpy as np 
import pyrealsense2 as rs
from scipy.ndimage import label, distance_transform_edt
from scipy.spatial.distance import cdist
import matplotlib.pyplot as plt
import matplotlib
import cv2
import torch
import math

'''
segmentation排序与路径规划。
原本针对食用菌二代，试图对采摘方向做路径规划：首先按坐标位置排序，然后寻找重叠蘑菇，对重叠蘑菇按照高度排序。（4.22）此进程仍有bug
食用菌三代增加了切槽和料槽，故路径规划上没有优化空间，则直接全部按照高度排序。（4.27）
yolov8-seg formattig.(2024/05/20)
'''
'''yolov8-seg <Results>
# Detection
result.boxes.xyxy   # box with xyxy format, (N, 4)
result.boxes.xywh   # box with xywh format, (N, 4)
result.boxes.xyxyn  # box with xyxy format but normalized, (N, 4)
result.boxes.xywhn  # box with xywh format but normalized, (N, 4)
result.boxes.conf   # confidence score, (N, 1)
result.boxes.cls    # cls, (N, 1)

# Classification
result.probs     # cls prob, (num_class, )

【250402】4th代码升级坐标处理方法
【250408】装备部署，根据new_position_seg.py中现行的必要功能修改此代码
'''

BIAS = 0
BIAS_MAXX = 7
BIAS_MAXY = 5
RSLUX = 848
RSLUY = 480
imgCenter = [RSLUX, RSLUY]

def calculate_normal_gradient(normal_map, mask, neighborhood_radius=10):
    """计算mask区域内法线图的变化速率，使用局部邻域内法线向量的余弦距离
    
    Args:
        normal_map: 法线图，形状为(H, W, 3)
        mask: 要计算的区域掩码，形状为(H, W)，布尔类型或二值类型
        neighborhood_radius: 计算局部变化率的邻域半径，默认5像素
        
    Returns:
        gradient_magnitude: 法线变化速率图，仅在mask区域内有值，形状为(H, W)
    """
    # 转换为numpy数组以便于操作
    normal_np = normal_map.numpy() if isinstance(normal_map, torch.Tensor) else normal_map
    
    # 确保mask是布尔类型
    mask = mask.astype(bool)
    
    # 创建全零的变化速率图
    H, W, _ = normal_np.shape
    gradient_magnitude = np.zeros((H, W), dtype=float)
    
    # 如果mask为空，直接返回全零图
    if not np.any(mask):
        return gradient_magnitude
    
    # 将法线向量标准化为单位向量
    normals_norm = np.linalg.norm(normal_np, axis=2, keepdims=True)
    normals_unit = normal_np / (normals_norm + 1e-6)
    
    # 获取mask内部的坐标
    y_coords, x_coords = np.where(mask)
    
    # 对mask内的每个点计算局部邻域内的变化率
    for i, (y, x) in enumerate(zip(y_coords, x_coords)):
        # 当前点的法线向量
        current_normal = normals_unit[y, x]
        
        # 定义邻域范围
        y_min = max(0, y - neighborhood_radius)
        y_max = min(H - 1, y + neighborhood_radius)
        x_min = max(0, x - neighborhood_radius)
        x_max = min(W - 1, x + neighborhood_radius)
        
        # 提取局部窗口
        local_window = mask[y_min:y_max+1, x_min:x_max+1]
        local_normals = normals_unit[y_min:y_max+1, x_min:x_max+1]
        
        # 计算局部窗口内所有mask内点与当前点的余弦距离
        # 排除当前点自身位置
        current_y_local, current_x_local = y - y_min, x - x_min
        
        # 创建一个同样大小的mask来排除当前点
        exclude_self = np.ones_like(local_window, dtype=bool)
        if 0 <= current_y_local < exclude_self.shape[0] and 0 <= current_x_local < exclude_self.shape[1]:
            exclude_self[current_y_local, current_x_local] = False
        
        # 只考虑局部窗口内且在mask内且不是自身的点
        valid_points = local_window & exclude_self
        
        if np.any(valid_points):
            # 展平有效点的坐标和对应的法线向量
            flat_indices = np.where(valid_points.flatten())[0]
            valid_normals = local_normals.reshape(-1, 3)[flat_indices]
            
            # 计算余弦相似度
            cosine_sims = np.dot(valid_normals, current_normal)
            cosine_sims = np.clip(cosine_sims, -1.0, 1.0)
            
            # 计算余弦距离
            distances = 1 - np.abs(cosine_sims)
            
            # 计算平均变化率
            gradient_magnitude[y, x] = np.mean(distances)
    
    # 仅在mask区域内进行归一化
    mask_values = gradient_magnitude[mask]
    mask_min = np.min(mask_values)
    mask_max = np.max(mask_values)
    
    if mask_max > mask_min:
        # 归一化到[0, 1]范围
        normalized_values = (mask_values - mask_min) / (mask_max - mask_min)
        
        # 将归一化后的值填回原始数组
        gradient_magnitude[mask] = normalized_values
    
    return gradient_magnitude

def find_stable_region(mask, gradient, target_ratio=0.2):
    """找到法线变化最小的连通区域（占mask面积的20%）"""
    # 仅考虑mask内部的梯度
    masked_gradient = gradient.copy()
    masked_gradient[mask == 0] = float('inf')  # 将mask外部的梯度设为无穷大
    
    # 计算目标面积
    mask_area = np.sum(mask)
    target_area = int(mask_area * target_ratio)
    
    # 基于梯度值排序
    flat_indices = np.argsort(masked_gradient.flatten())
    flat_mask = mask.flatten()
    valid_indices = [idx for idx in flat_indices if flat_mask[idx] > 0]
    
    # 选择最低梯度的点，直到达到目标面积
    stability_mask = np.zeros_like(mask, dtype=bool)
    h, w = mask.shape
    count = 0
    
    for idx in valid_indices:
        if count >= target_area:
            break
        y, x = idx // w, idx % w
        stability_mask[y, x] = True
        count += 1
    
    # 进行连通区域分析
    labeled_array, num_features = label(stability_mask)
    
    # 选择最大的连通区域
    if num_features > 0:
        sizes = np.bincount(labeled_array.flatten())[1:]  # 跳过背景
        largest_label = np.argmax(sizes) + 1  # +1 因为背景是0
        stable_region = (labeled_array == largest_label)
    else:
        stable_region = np.zeros_like(mask, dtype=bool)
    
    return stable_region

def find_mask_contours(mask):
    """找到mask的轮廓"""
    mask_uint8 = mask.astype(np.uint8) * 255
    contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:  # 如果没有找到轮廓
        return [], (0, 0)
    
    # 找到最大的轮廓
    max_contour = max(contours, key=cv2.contourArea)
    
    # 计算最大轮廓的几何中心
    M = cv2.moments(max_contour)
    if M["m00"] > 0:  # 确保轮廓的面积大于0
        cx = int(M["m10"] / M["m00"])
        cy = int(M["m01"] / M["m00"])
        center = (cx, cy)
    else:
        center = (0, 0)
    
    return contours, center

def find_mask_diameter(mask, sample_ratio=0.5):
    """
    寻找经过mask几何中心且长度最大的直径
    
    Args:
        mask: 2D布尔数组，表示mask区域
    
    Returns:
        start_point: 直径的起点坐标 (x, y)
        end_point: 直径的终点坐标 (x, y)
    """
    # 确保mask是布尔类型
    mask = mask.astype(np.uint8)
    
    # 计算mask的几何中心（质心）
    M = cv2.moments(mask)
    if M["m00"] == 0:
        return None, None  # 空mask
    center_x = int(M["m10"] / M["m00"])
    center_y = int(M["m01"] / M["m00"])
    center = (center_x, center_y)
    
    # 找到mask的轮廓
    contours, _ = find_mask_contours(mask)
    if not contours:
        return None, None
    
    # 合并所有轮廓点并按固定间隔采样以减少计算量
    all_contour_points = []
    for contour in contours:
        # 计算采样步长，确保至少取2个点
        step = max(1, int(1 / sample_ratio))
        sampled_points = [point[0] for point in contour[::step]]
        all_contour_points.extend(sampled_points)
    
    # 如果轮廓点太少，返回None
    if len(all_contour_points) < 2:
        return None, None
    
    # 寻找经过或接近中心的最长线段
    max_distance = 0
    best_pair = None
    
    # 设置接近中心的阈值（线到中心点的最大距离）
    threshold = max(5, np.sqrt(mask.shape[0]**2 + mask.shape[1]**2) * 0.05)  # 5或图像对角线的5%
    
    # 对每一对轮廓点检查
    n = len(all_contour_points)
    for i in range(n):
        for j in range(i+1, n):
            p1 = all_contour_points[i]
            p2 = all_contour_points[j]
            
            # 计算点到线的距离
            # 线段方程: (x-x1)/(x2-x1) = (y-y1)/(y2-y1)
            # 距离公式: |Ax + By + C|/sqrt(A^2 + B^2)，其中线的方程为Ax + By + C = 0
            
            if p1[0] == p2[0]:  # 垂直线
                distance_to_center = abs(center_x - p1[0])
            elif p1[1] == p2[1]:  # 水平线
                distance_to_center = abs(center_y - p1[1])
            else:
                # 一般情况
                A = (p2[1] - p1[1])
                B = (p1[0] - p2[0])
                C = (p2[0]*p1[1] - p1[0]*p2[1])
                distance_to_center = abs(A*center_x + B*center_y + C) / np.sqrt(A**2 + B**2)
            
            # 如果线段经过或接近中心，计算长度
            if distance_to_center <= threshold:
                # 计算线段长度
                distance = np.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
                
                if distance > max_distance:
                    max_distance = distance
                    best_pair = (p1, p2)
    
    if best_pair is None:
        return None, None, None, None, None
    
    p1, p2 = best_pair
    
    # 计算三等分点
    # 使用参数方程：P = P1 + t(P2-P1)，其中t分别为1/3和2/3
    third_point1 = (
        int(p1[0] + (p2[0] - p1[0]) / 3),  # x坐标
        int(p1[1] + (p2[1] - p1[1]) / 3)   # y坐标
    )
    
    third_point2 = (
        int(p1[0] + 2 * (p2[0] - p1[0]) / 3),  # x坐标
        int(p1[1] + 2 * (p2[1] - p1[1]) / 3)   # y坐标
    )
    
    return best_pair[0], best_pair[1], center, third_point1, third_point2

# 【250402】采摘点计算，坐标排序与路径规划
def pickPoints(imgrgb, savePath, results, normals, depth_frame, depth_intrin, TSIZE, serial_n):
    '''
    # Segmentation
    result.masks.data      # masks, (N, H, W)
    result.masks.xy        # x,y segments (pixels), List[segment] * N
    result.masks.xyn       # x,y segments (normalized), List[segment] * N
    '''
    normal_map = np.array(normals)
    masks = results.masks.data if results.masks is not None else []
    detections = results.boxes.xywh
    print("-----Detected {:d} mushroom in image-----\n".format(detections.size()[0]))
    dets = []
    minus = [0, 0, 0, 0]  # pickable, pixel size, w/h ratio, real size
    
    # 创建结果图像
    result_image = imgrgb.copy()
    
    # 创建热力图颜色映射
    colormap = matplotlib.colormaps.get_cmap('jet')
    
    # 对每个检测到的实例进行处理
    for i, mask_tensor in enumerate(masks):
        # get info of detections
        detection = detections[i]
        
        # Depricated some instance firstly
        
        # # filter based on box position for UI demo;
        # # left arm:  240~620
        # # right arm: 315~675
        # if serial_n == 1:  
        #     if detection[0] < 240 or detection[0] > 620 or detection[1] < 30 or detection[1] > 450:
        #         minus[0] += 1
        #         continue
        # else:
        #     if detection[0] < 315 or detection[0] > 675 or detection[1] < 30 or detection[1] > 450:
        #         minus[0] += 1
        #         continue
        
        # # filter based on box pixel size; 40mm mushroom model: 90 pixel
        # if detection[2] < 60 or detection[3] < 60:
        #     minus[1] += 1
        #     continue

        # 基于像素的长宽比判断
        if detection[2]/detection[3]>1.3 or detection[2]/detection[3]<0.7:
            minus[2] += 1
            continue
        
        # Process pick points now
        # 转换为numpy数组
        mask = mask_tensor.cpu().numpy()
        
        # # 计算mask边界并用红色可视化：可行
        # contours = find_mask_contours(mask)
        # cv2.drawContours(result_image, contours, -1, (255, 0, 0), 2)
        
        # 计算此mask区域内的法线变化速率
        mask_gradient = calculate_normal_gradient(normal_map, mask)

        # 可视化增强，提高区分度 - 使用伽马校正（增强对比度）
        gamma = 0.4  # 小于1的值会增强低值区域的对比度
        enhanced_gradient = np.power(mask_gradient, gamma)
        
        # 创建热力图
        # 首先创建与图像大小一致的全零数组
        heatmap = np.zeros_like(imgrgb)
        # 只处理mask区域
        mask_indices = np.where(mask)
        # 获取mask区域内的梯度值-增强可视化版
        mask_values = enhanced_gradient[mask_indices]
        # 应用colormap并转换为RGB
        # 将值映射到[0, 1]的范围
        normalized_values = mask_values.flatten()
        # 使用matplotlib的colormap
        colors = colormap(normalized_values)
        # 从RGBA转换为RGB并缩放到[0, 255]
        colors_rgb = (colors[:, :3] * 255).astype(np.uint8)
        # 将颜色填回到heatmap中
        for idx, (y, x) in enumerate(zip(*mask_indices)):
            heatmap[y, x] = colors_rgb[idx]
        
        # 将热力图应用到结果图像
        alpha = 0.5  # 热力图透明度
        mask_3d = np.repeat(mask[:, :, np.newaxis], 3, axis=2)
        result_image = np.where(
            mask_3d > 0,
            cv2.addWeighted(result_image, 1-alpha, heatmap, alpha, 0),
            result_image
        )
        
        # 找到法线变化最小的连通区域（占面积20%）
        stable_region = find_stable_region(mask, mask_gradient)
        stable_contours, safe_point = find_mask_contours(stable_region)  # 用连通区域的几何中心作为采摘点
        cv2.drawContours(result_image, stable_contours, -1, (0, 0, 0), 1)
        safe_x, safe_y = safe_point
        normal_vector = normal_map[safe_y, safe_x]  # 注意法线图的坐标顺序是 (y, x)

        # # draw normal arrow for visualization
        # length = np.linalg.norm(normal_vector)
        # normal_vector = normal_vector / length
        # x_end = safe_x + normal_vector[0] * 30
        # y_end = safe_y + normal_vector[1] * 30 * (-1)
        # # z_end = 0 + normal_vector[2] * 30
        # # 箭头起点
        # cv2.circle(result_image, (int(safe_x), int(safe_y)), 1, (0, 255, 0), -1)
        # # 箭头
        # cv2.arrowedLine(result_image, (int(safe_x), int(safe_y)), (int(x_end), int(y_end)), (0, 0, 255), 1)

        cv2.circle(result_image, safe_point, 3, (0, 0, 255), -1)  # 红色实心点

        # 找到经过几何中心的最长直径
        start_point, end_point, center, diameterP1, diameterP2 = find_mask_diameter(mask)
        if start_point is not None and end_point is not None:
            # 顺便把几何中心绘制出来
            cv2.circle(result_image, center, 3, (255, 255, 255), -1)  # 白色实心点
            # 绘制直径线段（白色，粗细为2）
            cv2.line(result_image, start_point, end_point, (255, 255, 255), 1)
            # 把两个测量点绘制出来
            cv2.circle(result_image, diameterP1, 3, (0, 255, 0), -1)  # 绿色实心点
            cv2.circle(result_image, diameterP2, 3, (0, 255, 0), -1)  # 绿色实心点
        
        # 检测采摘点的深度值是否存在判断
        pick_depth = depth_frame.get_distance(int(safe_x), int(safe_y))
        if pick_depth == 0:  # 如果深度信息为空
            print('=====NULL depth info at ({:.0f},{:.0f}) and set to 0.245====='.format(safe_x.cpu().numpy(), safe_y.cpu().numpy()))
            pick_depth = 0.2450

        rf1_depth = depth_frame.get_distance(int(diameterP1[0]), int(diameterP1[1]))
        rf2_depth = depth_frame.get_distance(int(diameterP2[0]), int(diameterP2[1]))
        rf1_point = rs.rs2_deproject_pixel_to_point(depth_intrin, [int(diameterP1[0]), int(diameterP1[1])], rf1_depth)
        rf2_point = rs.rs2_deproject_pixel_to_point(depth_intrin, [int(diameterP2[0]), int(diameterP2[1])], rf2_depth)
        
        # calculate the diameter of two rf points
        size = math.sqrt(sum((a-b) ** 2 for a, b in zip(rf1_point, rf2_point))) *3 + 0.0005
        
        if size < TSIZE:
            print('=====Diameter too small: {:.1f}\n'.format(size*1000))
            minus[3] += 1
            continue
        print('-----Diameter: {:.1f}\n'.format(size*1000))
        # put size text
        labelsize = f"s{size*1000:.1f}"
        possize = (int(center[0]-20), int(center[1]+22))
        cv2.putText(result_image, labelsize,
                    possize,
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.4,  # font scale
                    (0, 255, 0),  # 绿色指示尺寸
                    1)  # line type

        # 获取采摘点的深度坐标 picking point!
        pick_point = rs.rs2_deproject_pixel_to_point(depth_intrin, [int(safe_x), int(safe_y)], pick_depth)
        # put depth text
        labeldepth = f"d{pick_point[2]*1000:.1f}"
        posdepth = (int(safe_x-20), int(safe_y-3))
        cv2.putText(result_image, labeldepth,
                    posdepth,
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.4,  # font scale
                    (0, 0, 255),  # 红色指示深度
                    1)  # line type

        # grab useful info from processed results
        dets.append([pick_point[0], pick_point[1], pick_point[2], normal_vector[0], normal_vector[1], normal_vector[2], size])

    print('=====Depreciated Position limit, Pixel size, W/H ratio, Real size: ', minus)
    # 按照位置信息从上到下和从下到上
    aft_dets = sorted(dets, key=lambda d: d[2])  # 按照top值从小到大排序，双臂则增加一种从大到小
    print('-----Output {} objects in image'.format(len(aft_dets)))

    cv2.imwrite(savePath, result_image)

    return aft_dets