from ultralytics import YOLO
import time
from datetime import datetime
import cv2
import matplotlib
import numpy as np
import math
import random
from scipy import ndimage

'''
从test_Run_4th-0708.py中提取核心视觉算法，针对RGB图像开发路径规划
【250725】在真实蘑菇图片上实现基于凸包的采摘顺序规划--直观评价不可行
【250728】尝试实现基于圆度的采摘顺序规划
'''

# 凸包算法相关函数

def get_start_point(points):
    """
    返回points中纵坐标最小的点的索引，如果有多个纵坐标最小的点则返回其中横坐标最小的那个
    :param points:
    :return:
    """
    min_index = 0
    n = len(points)
    for i in range(0, n):
        if points[i][1] < points[min_index][1] or (points[i][1] == points[min_index][1] and points[i][0] < points[min_index][0]):
            min_index = i
    return min_index


def sort_polar_angle_cos(points, start_point):
    """
    按照与中心点的极角进行排序，使用的是余弦的方法
    :param points: 需要排序的点
    :param start_point: 起始点
    :return:
    """
    n = len(points)
    cos_value = []
    rank = []
    norm_list = []
    for i in range(0, n):
        point_ = points[i]
        point = [point_[0]-start_point[0], point_[1]-start_point[1]]
        rank.append(i)
        norm_value = math.sqrt(point[0]*point[0] + point[1]*point[1])
        norm_list.append(norm_value)
        if norm_value == 0:
            cos_value.append(1)
        else:
            cos_value.append(point[0] / norm_value)

    for i in range(0, n-1):
        index = i + 1
        while index > 0:
            if cos_value[index] > cos_value[index-1] or (cos_value[index] == cos_value[index-1] and norm_list[index] > norm_list[index-1]):
                temp = cos_value[index]
                temp_rank = rank[index]
                temp_norm = norm_list[index]
                cos_value[index] = cos_value[index-1]
                rank[index] = rank[index-1]
                norm_list[index] = norm_list[index-1]
                cos_value[index-1] = temp
                rank[index-1] = temp_rank
                norm_list[index-1] = temp_norm
                index = index-1
            else:
                break
    sorted_points = []
    for i in rank:
        sorted_points.append(points[i])

    return sorted_points


def vector_angle(vector):
    """
    返回一个向量与向量 [1, 0]之间的夹角， 这个夹角是指从[1, 0]沿逆时针方向旋转多少度能到达这个向量
    :param vector:
    :return:
    """
    norm_ = math.sqrt(vector[0]*vector[0] + vector[1]*vector[1])
    if norm_ == 0:
        return 0

    angle = math.acos(vector[0]/norm_)
    if vector[1] >= 0:
        return angle
    else:
        return 2*math.pi - angle


def coss_multi(v1, v2):
    """
    计算两个向量的叉乘
    :param v1:
    :param v2:
    :return:
    """
    return v1[0]*v2[1] - v1[1]*v2[0]


def graham_scan(points):
    """
    Graham扫描法计算凸包
    """
    if len(points) < 3:
        return points
        
    points_copy = points.copy()
    bottom_index = get_start_point(points_copy)
    bottom_point = points_copy.pop(bottom_index)
    sorted_points = sort_polar_angle_cos(points_copy, bottom_point)

    m = len(sorted_points)
    if m < 2:
        return [bottom_point] + sorted_points

    stack = []
    stack.append(bottom_point)
    stack.append(sorted_points[0])
    stack.append(sorted_points[1])

    for i in range(2, m):
        length = len(stack)
        top = stack[length-1]
        next_top = stack[length-2]
        v1 = [sorted_points[i][0]-next_top[0], sorted_points[i][1]-next_top[1]]
        v2 = [top[0]-next_top[0], top[1]-next_top[1]]

        while coss_multi(v1, v2) >= 0:
            stack.pop()
            length = len(stack)
            if length < 2:
                break
            top = stack[length-1]
            next_top = stack[length-2]
            v1 = [sorted_points[i][0] - next_top[0], sorted_points[i][1] - next_top[1]]
            v2 = [top[0] - next_top[0], top[1] - next_top[1]]

        stack.append(sorted_points[i])

    return stack


def sort_convex_hull(convex_hull):
    """
    以凸包顶点为输入，根据每一个凸包顶点与相邻两个点形成的内角从小到大的顺序对凸包的边界顶点进行排序
    """
    n = len(convex_hull)
    if n < 3:
        return convex_hull
        
    sorted_convex_hull = []
    for i in range(0, n):
        point = convex_hull[i]
        pre_point = convex_hull[i-1]
        next_point = convex_hull[(i+1)%n]
        v1 = [pre_point[0]-point[0], pre_point[1]-point[1]]
        v2 = [next_point[0]-point[0], next_point[1]-point[1]]
        angle = vector_angle(v2) - vector_angle(v1)
        if angle < 0:
            angle += 2*math.pi
        sorted_convex_hull.append([point, angle])

    sorted_convex_hull.sort(key=lambda x: x[1], reverse=True)
    result = []
    for item in sorted_convex_hull:
        result.append(item[0])

    return result


def convex_path_planning(dets, imgrgb, savePath=None):
    """
    基于凸包的路径规划函数：对检测到的目标点进行端到端排序
    
    Args:
        dets: 检测结果列表，每个元素格式为 [center_x, center_y, z, rx, ry, size]
        imgrgb: 原始RGB图像
        savePath: 可选的保存路径，如果提供将保存可视化结果
    
    Returns:
        ordered_dets: 按照凸包路径规划排序后的检测结果
        final_order: 每个点的最终序号字典
    """
    if len(dets) < 3:
        print("点数少于3个，无需进行凸包路径规划")
        return dets, {tuple(det[:2]): i+1 for i, det in enumerate(dets)}
    
    # 提取中心点坐标用于凸包计算
    points = [(int(det[0]), int(det[1])) for det in dets]
    original_dets = [det for det in dets]  # 保存原始检测结果
    points_copy = [point for point in points]
    final_order = {}  # 存储每个点的最终序号
    current_order = 1
    
    print(f"开始凸包路径规划，共{len(points)}个目标点")
    
    # 进行n-2轮排序（当点数<=2时停止）
    while len(points_copy) > 2:
        # 计算当前点集的凸包
        convex_hull = graham_scan(points_copy)
        if convex_hull and len(convex_hull) > 2:
            # 对凸包顶点按内角排序
            sorted_hull = sort_convex_hull(convex_hull)
            if sorted_hull:
                # 获取排序第一的点
                first_point = sorted_hull[0]
                
                # 记录这个点的最终序号
                final_order[first_point] = current_order
                
                # 从当前点集中移除这个点
                index_to_remove = next((i for i, point in enumerate(points_copy) 
                                      if point == first_point), None)
                if index_to_remove is not None:
                    del points_copy[index_to_remove]
                
                current_order += 1
        else:
            break
    
    # 为剩余的点（如果有的话）分配序号
    for remaining_point in points_copy:
        final_order[remaining_point] = current_order
        current_order += 1
    
    # 根据最终序号对原始检测结果进行排序
    ordered_dets = []
    for order in range(1, len(dets) + 1):
        for point, point_order in final_order.items():
            if point_order == order:
                # 找到对应的原始检测结果
                for det in original_dets:
                    if (int(det[0]), int(det[1])) == point:
                        ordered_dets.append(det)
                        break
                break
    
    # 如果提供了保存路径，生成可视化结果
    if savePath:
        result_image = imgrgb.copy()
        
        # 绘制所有检测点
        for i, det in enumerate(original_dets):
            center = (int(det[0]), int(det[1]))
            cv2.circle(result_image, center, 5, (0, 0, 255), -1)  # 红色实心圆
            
            # 标注最终序号
            if center in final_order:
                order_text = str(final_order[center])
                cv2.putText(result_image, order_text, 
                           (center[0] + 10, center[1] - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
        
        # 绘制最终的凸包
        if len(points) > 2:
            final_convex_hull = graham_scan(points)
            if final_convex_hull and len(final_convex_hull) > 2:
                # 绘制凸包边界
                for i in range(len(final_convex_hull)):
                    start = final_convex_hull[i]
                    end = final_convex_hull[(i + 1) % len(final_convex_hull)]
                    cv2.line(result_image, start, end, (255, 0, 0), 2)  # 蓝色线条
        
        # # 绘制路径规划顺序（用箭头连接）
        # if len(ordered_dets) > 1:
        #     for i in range(len(ordered_dets) - 1):
        #         start = (int(ordered_dets[i][0]), int(ordered_dets[i][1]))
        #         end = (int(ordered_dets[i+1][0]), int(ordered_dets[i+1][1]))
        #         cv2.arrowedLine(result_image, start, end, (0, 255, 255), 2)  # 黄色箭头
        
        cv2.imwrite(savePath, result_image)
        print(f"路径规划可视化结果已保存到: {savePath}")
    
    print(f"凸包路径规划完成，排序结果：")
    for i, det in enumerate(ordered_dets):
        center = (int(det[0]), int(det[1]))
        if center in final_order:
            print(f"序号{final_order[center]}: 点({center[0]}, {center[1]})")
    
    return ordered_dets, final_order


OFFSETX = -2
OFFSETY = -4
SIZEBIAS = 0

def load_models(yolo_path, marigold_path):
    """加载YOLO和Marigold模型"""
    # 加载YOLO模型
    yolo_model = YOLO(yolo_path)
    print('yolo model done')
    
    # # 加载Marigold模型
    # pipe_normal = diffusers.MarigoldNormalsPipeline.from_pretrained(
    #     marigold_path, variant="fp16", torch_dtype=torch.float16, local_files_only=True
    # ).to("cuda")
    # print('diffusion model done')
    
    # return yolo_model, pipe_normal
    
    return yolo_model

# 处理yolo8-seg输出mask的固有偏移
def apply_mask_offset(mask, offset_x, offset_y):
    """
    对mask应用偏移校正
    
    Args:
        mask: 2D布尔数组或二值数组，表示mask区域
        offset_x: x方向偏移量（负值向左，正值向右）
        offset_y: y方向偏移量（负值向上，正值向下）
    
    Returns:
        offset_mask: 偏移后的mask
    """
    if offset_x == 0 and offset_y == 0:
        return mask
    
    h, w = mask.shape
    offset_mask = np.zeros_like(mask)
    
    # 使用numpy切片实现高效偏移
    # 计算有效的源区域和目标区域
    if offset_y < 0:  # 向上移动
        src_y_slice = slice(-offset_y, None)
        dst_y_slice = slice(0, h + offset_y)
    elif offset_y > 0:  # 向下移动
        src_y_slice = slice(0, h - offset_y)
        dst_y_slice = slice(offset_y, None)
    else:  # 不移动
        src_y_slice = slice(None)
        dst_y_slice = slice(None)
    
    if offset_x < 0:  # 向左移动
        src_x_slice = slice(-offset_x, None)
        dst_x_slice = slice(0, w + offset_x)
    elif offset_x > 0:  # 向右移动
        src_x_slice = slice(0, w - offset_x)
        dst_x_slice = slice(offset_x, None)
    else:  # 不移动
        src_x_slice = slice(None)
        dst_x_slice = slice(None)
    
    # 执行偏移复制
    offset_mask[dst_y_slice, dst_x_slice] = mask[src_y_slice, src_x_slice]
    
    return offset_mask

def calculate_mask_circularity(mask):
    """
    计算mask的圆度（Circularity）
    
    Args:
        mask: 2D布尔数组或二值数组，表示mask区域
    
    Returns:
        circularity: 圆度值，完全圆形为1，其他形状小于1
    """
    # 确保mask是uint8类型
    mask_uint8 = mask.astype(np.uint8) * 255
    
    # 找到轮廓
    contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        return 0.0
    
    # 选择最大的轮廓
    max_contour = max(contours, key=cv2.contourArea)
    
    # 计算面积和周长
    area = cv2.contourArea(max_contour)
    perimeter = cv2.arcLength(max_contour, True)
    
    # 避免除零错误
    if perimeter == 0 or area == 0:
        return 0.0
    
    # 计算圆度：4π × 面积 / 周长²
    circularity = 4 * np.pi * area / (perimeter ** 2)
    
    return circularity

# mask边界优化
def smooth_mask_gaussian_ellipse(mask, sigma=1.5, ellipse_padding=0.05):
    """结合高斯滤波和椭圆拟合的混合方法
    sigma: 1.0-2.0，值越大越平滑
    ellipse_padding: 0.02-0.1，控制椭圆扩张程度
    如果目标很小，可以减小sigma
    """
    # 先用高斯滤波初步平滑
    gaussian_smoothed = ndimage.gaussian_filter(mask.astype(np.float32), sigma=sigma)
    gaussian_mask = (gaussian_smoothed > 0.3).astype(np.uint8)
    
    # 找到轮廓进行椭圆拟合
    contours, _ = cv2.findContours(gaussian_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    if not contours:
        return mask
    
    max_contour = max(contours, key=cv2.contourArea)
    if len(max_contour) < 5:
        return gaussian_mask
    
    # 拟合椭圆
    ellipse = cv2.fitEllipse(max_contour)
    center, (w, h), angle = ellipse
    
    # 创建椭圆mask
    ellipse_mask = np.zeros_like(mask)
    expanded_ellipse = (center, (w * (1 + ellipse_padding), h * (1 + ellipse_padding)), angle)
    cv2.ellipse(ellipse_mask, expanded_ellipse, 1, -1)
    
    # 与原始mask取交集，确保不超出原始范围太多
    original_dilated = cv2.dilate(mask, cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5)), iterations=1)
    result = cv2.bitwise_and(ellipse_mask, original_dilated)
    
    return result

def find_mask_contours(mask):
    """找到mask的轮廓"""
    mask_uint8 = mask.astype(np.uint8) * 255
    contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:  # 如果没有找到轮廓
        return [], (0, 0)
    
    # 找到最大的轮廓
    max_contour = max(contours, key=cv2.contourArea)
    
    # 计算最大轮廓的几何中心
    M = cv2.moments(max_contour)
    if M["m00"] > 0:  # 确保轮廓的面积大于0
        cx = int(M["m10"] / M["m00"])
        cy = int(M["m01"] / M["m00"])
        center = (cx, cy)
    else:
        center = (0, 0)
    
    return contours, center

def find_mask_diameter(mask, sample_ratio=0.5):
    """
    寻找经过mask几何中心且长度最大的直径
    
    Args:
        mask: 2D布尔数组，表示mask区域
    
    Returns:
        start_point: 直径的起点坐标 (x, y)
        end_point: 直径的终点坐标 (x, y)
    """
    # 确保mask是布尔类型
    mask = mask.astype(np.uint8)
    # mask = smooth_mask_gaussian_ellipse(mask_ori, sigma=3.0, ellipse_padding=0.1)
    
    # 计算mask的几何中心（质心）
    M = cv2.moments(mask)
    if M["m00"] == 0:
        return None, None  # 空mask
    center_x = int(M["m10"] / M["m00"])
    center_y = int(M["m01"] / M["m00"])
    center = (center_x, center_y)
    
    # 找到mask的轮廓
    contours, _ = find_mask_contours(mask)
    contours = [max(contours, key=cv2.contourArea)]
    if not contours:
        return None, None
    
    # 合并所有轮廓点并按固定间隔采样以减少计算量
    all_contour_points = []
    for contour in contours:
        # 计算采样步长，确保至少取2个点
        step = max(1, int(1 / sample_ratio))
        sampled_points = [point[0] for point in contour[::step]]
        all_contour_points.extend(sampled_points)
    
    # 如果轮廓点太少，返回None
    if len(all_contour_points) < 2:
        return None, None
    
    # 寻找经过或接近中心的最长线段
    max_distance = 0
    best_pair = None
    
    # 设置接近中心的阈值（线到中心点的最大距离）
    threshold = max(5, np.sqrt(mask.shape[0]**2 + mask.shape[1]**2) * 0.05)  # 5或图像对角线的5%
    
    # 对每一对轮廓点检查
    n = len(all_contour_points)
    for i in range(n):
        for j in range(i+1, n):
            p1 = all_contour_points[i]
            p2 = all_contour_points[j]
            
            # 计算点到线的距离
            # 线段方程: (x-x1)/(x2-x1) = (y-y1)/(y2-y1)
            # 距离公式: |Ax + By + C|/sqrt(A^2 + B^2)，其中线的方程为Ax + By + C = 0
            
            if p1[0] == p2[0]:  # 垂直线
                distance_to_center = abs(center_x - p1[0])
            elif p1[1] == p2[1]:  # 水平线
                distance_to_center = abs(center_y - p1[1])
            else:
                # 一般情况
                A = (p2[1] - p1[1])
                B = (p1[0] - p2[0])
                C = (p2[0]*p1[1] - p1[0]*p2[1])
                distance_to_center = abs(A*center_x + B*center_y + C) / np.sqrt(A**2 + B**2)
            
            # 如果线段经过或接近中心，计算长度
            if distance_to_center <= threshold:
                # 计算线段长度
                distance = np.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
                
                if distance > max_distance:
                    max_distance = distance
                    best_pair = (p1, p2)
    
    if best_pair is None:
        return None, None, None, None, None
    
    p1, p2 = best_pair
    
    # 计算三等分点
    # 使用参数方程：P = P1 + t(P2-P1)，其中t分别为1/3和2/3
    third_point1 = (
        int(p1[0] + (p2[0] - p1[0]) / 3),  # x坐标
        int(p1[1] + (p2[1] - p1[1]) / 3)   # y坐标
    )
    
    third_point2 = (
        int(p1[0] + 2 * (p2[0] - p1[0]) / 3),  # x坐标
        int(p1[1] + 2 * (p2[1] - p1[1]) / 3)   # y坐标
    )
    
    return best_pair[0], best_pair[1], center, third_point1, third_point2, contours

# 【250725】在本地开发测试路径规划功能
def pickPoints(imgrgb, savePath, results):
    '''
    # Segmentation
    result.masks.data      # masks, (N, H, W)
    result.masks.xy        # x,y segments (pixels), List[segment] * N
    result.masks.xyn       # x,y segments (normalized), List[segment] * N
    '''
    # normal_map = np.array(normals)
    # print('>>>>>>>>>>>>> results: ', results)
    masks = results.masks.data if results.masks is not None else []
    
    # 【250708】debug
    # print('>>>>>>>>>>>>> masks shape: ', masks[0].shape)
    # print('>>>>>>>>>>>>> rgb shape  : ', imgrgb.shape)
    
    detections = results.boxes.xywh
    print("-----Detected {:d} mushroom in image-----\n".format(detections.size()[0]))
    dets = []
    minus = [0, 0, 0, 0]  # pickable, pixel size, w/h ratio, real size
    
    # 创建结果图像
    result_image = imgrgb.copy()
    
    # 创建热力图颜色映射
    colormap = matplotlib.colormaps.get_cmap('jet')
    
    # 【250728】先根据所有mask的圆度进行排序
    if len(masks) > 0:
        # 计算每个mask的圆度
        mask_circularity_list = []
        for i, mask_tensor in enumerate(masks):
            mask = mask_tensor.cpu().numpy()
            mask = apply_mask_offset(mask, OFFSETX, OFFSETY)
            circularity = calculate_mask_circularity(mask)
            mask_circularity_list.append((i, circularity, mask_tensor))
        
        # 按圆度从高到低排序（越接近正圆形的排在前面）
        mask_circularity_list.sort(key=lambda x: x[1], reverse=True)
        print("-----按圆度排序后的结果-----")
        for idx, (original_idx, circularity, _) in enumerate(mask_circularity_list):
            print(f"排序第{idx+1}: 原始索引{original_idx}, 圆度值: {circularity:.4f}")
        print()
        
        # 重新排列masks和detections
        sorted_masks = [item[2] for item in mask_circularity_list]
        sorted_indices = [item[0] for item in mask_circularity_list]
        sorted_detections = [detections[idx] for idx in sorted_indices]
    else:
        sorted_masks = masks
        sorted_detections = detections

    # 对每个检测到的实例进行处理（现在按圆度排序）
    valid_dets_count = 0  # 用于记录有效检测数量，用于显示排序
    for i, mask_tensor in enumerate(sorted_masks):

        # get info of detections
        detection = sorted_detections[i]

        # 基于像素的大小判断
        if detection[2]*detection[3]<1500:
            minus[1] += 1
            continue

        # 基于像素的长宽比判断
        if detection[2]/detection[3]>1.3 or detection[2]/detection[3]<0.7:
            minus[2] += 1
            continue
        
        # Process pick points now
        # 转换为numpy数组
        mask = mask_tensor.cpu().numpy()
        # mask = cv2.resize(mask, (RSLUX, RSLUY), interpolation=cv2.INTER_NEAREST)
        # print('>>>>>>>>>>>>> masks shape: ', mask.shape)
        mask = apply_mask_offset(mask, OFFSETX, OFFSETY)

        # 计算当前mask的圆度（用于显示）
        current_circularity = calculate_mask_circularity(mask)

        # 找到mask的质心和经过几何中心的最长直径
        start_point, end_point, center, diameterP1, diameterP2, contours = find_mask_diameter(mask)
        cv2.drawContours(result_image, contours, -1, (0, 0, 255), 2)
        if start_point is not None and end_point is not None:
            # 顺便把几何中心绘制出来
            cv2.circle(result_image, center, 3, (0, 0, 255), -1)  # 红色实心点
            # 绘制直径线段（白色，粗细为2）
            cv2.line(result_image, start_point, end_point, (255, 255, 255), 1)
            # 把两个测量点绘制出来
            cv2.circle(result_image, diameterP1, 3, (0, 255, 0), -1)  # 绿色实心点
            cv2.circle(result_image, diameterP2, 3, (0, 255, 0), -1)  # 绿色实心点
            
            # 【250728】在图像上显示圆度排序和数值
            valid_dets_count += 1
            # text = f"{valid_dets_count} {current_circularity:.3f}"
            text = f"{valid_dets_count}"  # 只显示顺序
            # 在中心点附近显示文本，使用白色背景黑色字体以提高可读性
            text_size = cv2.getTextSize(text, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            text_x = center[0] - text_size[0] // 2
            text_y = center[1] - 8
            # # 绘制白色背景矩形
            # cv2.rectangle(result_image, (text_x-2, text_y-text_size[1]-2), 
            #              (text_x+text_size[0]+2, text_y+2), (255, 255, 255), -1)
            # 绘制绿色文字
            cv2.putText(result_image, text, (text_x, text_y), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

        # diameter=pixel distance between two points
        size = math.sqrt((diameterP1[0] - diameterP2[0]) ** 2 + (diameterP1[1] - diameterP2[1]) ** 2) * 3 + SIZEBIAS

        dets.append([center[0], center[1], 0.0, 0.0, 0.0, size])

    print('=====Depreciated Depth holl, Pixel size, W/H ratio, Real size: ', minus)
    
    # 【250728】现在dets已经按照圆度排序，无需额外排序
    aft_dets = dets
    print(f'-----按圆度排序完成，共{len(aft_dets)}个目标点按序排列-----')
    print('-----Output {} objects in image'.format(len(aft_dets)))
    
    # 路径规划
    # 1.按照高度从高到低
    # aft_dets = sorted(dets, key=lambda d: d[2])  # 按照top值从小到大排序
    
    # 2.基于凸包算法从外到内
    # if len(dets) >= 3:
    #     # 生成路径规划可视化图片路径
    #     planning_savePath = savePath.replace('.jpg', '_planning.jpg') if savePath.endswith('.jpg') else savePath + '_planning.jpg'
    #     aft_dets, final_order = convex_path_planning(dets, result_image, planning_savePath)
    #     print(f'-----凸包路径规划完成，共{len(aft_dets)}个目标点按序排列-----')
    # else:
    #     aft_dets = dets
    #     print(f'-----目标点少于3个，跳过凸包路径规划，共{len(aft_dets)}个目标点-----')
    # print('-----Output {} objects in image'.format(len(aft_dets)))
    
    # 3.基于圆度指标：已在主循环中实现

    cv2.imwrite(savePath, result_image)

    return aft_dets

if __name__ == '__main__':
    
    YOLOPATH = "/home/<USER>/yolov8-1/runs/segment/train30/weights/best.pt"
    NORMALPATH = "prs-eth/marigold-normals-lcm-v0-1"
    THRESHOLD = 0.7
    tempDir = '/home/<USER>/dev-utils/mushroom_4th/results_images/'
    # set the model
    # yolo_model, normal_pipe = load_models(YOLOPATH, NORMALPATH)
    yolo_model = load_models(YOLOPATH, NORMALPATH)
    print('-----Model init done!-----\n')

    test_colorimg_path = '/home/<USER>/dev-utils/mushroom_4th/test_images/1_20250609140725_color_LBefore.jpg'
    color_imagePath = test_colorimg_path
    color_image = cv2.imread(color_imagePath)

    # model inference
    results = yolo_model(color_imagePath, conf=THRESHOLD, max_det=130)
    # print('-----results.mask: ', results[0].masks.data.size())
    print('-----YOLO model inference done-----\n')

    # formatting inference results and results visualization
    result_imagePath = tempDir + color_imagePath.split('/')[-1][:-4] + '_vis.jpg'

    start = time.time()
    aft_dets = pickPoints(color_image, result_imagePath, results[0])
    print('-----Pickpoints process time: ', time.time()-start)