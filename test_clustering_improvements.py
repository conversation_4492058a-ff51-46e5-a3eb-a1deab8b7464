#!/usr/bin/env python3
"""
测试改进的聚类系统
包括K-means改进和DBSCAN实现
"""

import numpy as np
import sys
import os

# 添加src路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src_WH250801'))

from position_5th import (
    get_clustering_strategy, 
    KMeansClusteringStrategy, 
    DBSCANClusteringStrategy,
    visualize_clusters_on_image
)

def generate_test_data():
    """生成测试数据 - 模拟用户遇到的40个目标分布情况"""
    np.random.seed(42)  # 确保结果可重现
    
    # 创建3个明显的集群 + 一些噪声点
    cluster1 = np.random.normal([100, 100], [15, 15], (15, 2))  # 15个点
    cluster2 = np.random.normal([300, 150], [20, 10], (18, 2))  # 18个点
    cluster3 = np.random.normal([200, 300], [12, 18], (12, 2))  # 12个点
    
    # 添加一些噪声点
    noise = np.random.uniform([50, 50], [350, 350], (5, 2))  # 5个噪声点
    
    # 合并所有点
    all_points = np.vstack([cluster1, cluster2, cluster3, noise])
    
    # 创建目标信息列表（模拟position_5th.py中的targets_info格式）
    targets_info = []
    for i, point in enumerate(all_points):
        target = {
            'pixel_x': point[0],
            'pixel_y': point[1],
            'size': np.random.uniform(0.02, 0.08),  # 模拟尺寸
            'world_point': [point[0]/100, point[1]/100, np.random.uniform(0.3, 0.8)]  # 模拟3D点
        }
        targets_info.append(target)
    
    print(f"生成了{len(targets_info)}个测试目标")
    return targets_info

def test_kmeans_clustering():
    """测试改进的K-means聚类"""
    print("\n=== 测试K-means聚类 ===")
    
    targets_info = generate_test_data()
    strategy = KMeansClusteringStrategy()
    
    # 测试聚类
    result = strategy.cluster_targets(targets_info)
    
    if result:
        print(f"K-means聚类成功:")
        print(f"  算法: {result['algorithm']}")
        print(f"  聚类数量: {len(result['cluster_centers'])}")
        print(f"  聚类大小: {result['cluster_sizes']}")
        print(f"  排序后的聚类: {result['sorted_clusters']}")
        
        # 检查聚类质量
        n_clusters = len(result['cluster_centers'])
        if n_clusters >= 3:
            print("✓ K-means成功识别出多个聚类（符合预期）")
        else:
            print(f"⚠ K-means只识别出{n_clusters}个聚类，可能需要进一步调优")
            
    else:
        print("✗ K-means聚类失败")
    
    return result

def test_dbscan_clustering():
    """测试DBSCAN聚类"""
    print("\n=== 测试DBSCAN聚类 ===")
    
    targets_info = generate_test_data()
    strategy = DBSCANClusteringStrategy()
    
    # 测试聚类
    result = strategy.cluster_targets(targets_info)
    
    if result:
        print(f"DBSCAN聚类成功:")
        print(f"  算法: {result['algorithm']}")
        print(f"  聚类数量: {len(result['cluster_centers'])}")
        print(f"  聚类大小: {result['cluster_sizes']}")
        print(f"  噪声点数量: {len(result.get('noise_points', []))}")
        print(f"  排序后的聚类: {result['sorted_clusters']}")
        
        # 检查聚类质量
        n_clusters = len(result['cluster_centers'])
        n_noise = len(result.get('noise_points', []))
        if n_clusters >= 3:
            print("✓ DBSCAN成功识别出多个聚类（符合预期）")
        if n_noise > 0:
            print(f"✓ DBSCAN成功识别出{n_noise}个噪声点")
            
    else:
        print("✗ DBSCAN聚类失败")
    
    return result

def test_strategy_selection():
    """测试策略选择功能"""
    print("\n=== 测试策略选择 ===")
    
    # 测试不同的聚类策略选择
    for cluster_type in [1, 2, 3]:
        strategy = get_clustering_strategy(cluster_type)
        if cluster_type == 1:
            print("✓ 策略1: K-means聚类策略创建成功")
            assert isinstance(strategy, KMeansClusteringStrategy)
        elif cluster_type == 2:
            print("✓ 策略2: DBSCAN聚类策略创建成功")
            assert isinstance(strategy, DBSCANClusteringStrategy)
        elif cluster_type == 3:
            print("✓ 策略3: 关闭聚类（返回None）")
            assert strategy is None

def test_visualization():
    """测试可视化功能"""
    print("\n=== 测试可视化功能 ===")
    
    try:
        import cv2
        
        # 创建测试图像
        test_image = np.ones((400, 400, 3), dtype=np.uint8) * 255
        targets_info = generate_test_data()
        
        # 测试K-means可视化
        kmeans_strategy = KMeansClusteringStrategy()
        kmeans_result = kmeans_strategy.cluster_targets(targets_info)
        
        if kmeans_result:
            vis_image = visualize_clusters_on_image(test_image, targets_info, kmeans_result)
            print("✓ K-means可视化功能正常")
        
        # 测试DBSCAN可视化
        dbscan_strategy = DBSCANClusteringStrategy()
        dbscan_result = dbscan_strategy.cluster_targets(targets_info)
        
        if dbscan_result:
            vis_image = visualize_clusters_on_image(test_image, targets_info, dbscan_result)
            print("✓ DBSCAN可视化功能正常")
            
    except ImportError:
        print("⚠ OpenCV未安装，跳过可视化测试")

def main():
    """主测试函数"""
    print("开始测试改进的聚类系统...")
    
    try:
        # 测试策略选择
        test_strategy_selection()
        
        # 测试K-means聚类
        kmeans_result = test_kmeans_clustering()
        
        # 测试DBSCAN聚类
        dbscan_result = test_dbscan_clustering()
        
        # 测试可视化
        test_visualization()
        
        print("\n=== 测试总结 ===")
        
        # 比较两种算法的结果
        if kmeans_result and dbscan_result:
            kmeans_clusters = len(kmeans_result['cluster_centers'])
            dbscan_clusters = len(dbscan_result['cluster_centers'])
            dbscan_noise = len(dbscan_result.get('noise_points', []))
            
            print(f"K-means发现了{kmeans_clusters}个聚类")
            print(f"DBSCAN发现了{dbscan_clusters}个聚类和{dbscan_noise}个噪声点")
            
            if dbscan_clusters >= kmeans_clusters:
                print("✓ DBSCAN在处理噪声和不规则形状方面表现更好")
            if kmeans_clusters >= 3:
                print("✓ 改进的K-means能够发现多个聚类（解决了原来只有2个聚类的问题）")
        
        print("聚类系统测试完成！")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
