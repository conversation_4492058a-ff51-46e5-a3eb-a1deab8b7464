当前项目是某蘑菇采摘机器人的视觉系统第4版，其中src_WH250801/test_Run_4thnew.py和src_WH250801/position_4th.py是第4版系统的核心文件。在原第4版代码中，对yolov8模型所输出的实例分割results的后处理功能比较单一，比如采摘点是mask的几何中心、直径是沿mask内最长线段的方向上计算、采摘顺序是按照采摘点得深度值从小到大排列、以及并没有利用上marigold模型计算的法线图等。

现在我想重写整个代码架构，以将其升级为第5个版本。此次升级代码的主要目的，是为了让视觉系统在对图片中每一个mask目标的采摘点选取、直径判定、采摘顺序规划和法线信息的输出与否等后处理功能上支持多种策略的实现及其快速切换，以更好的应对不同的实验场景；同时增加一些时间计算、数据记录等优化功能。

我已经将原来的src_WH250801/test_Run_4thnew.py和src_WH250801/position_4th.py复制为了src_WH250801/test_Run_5th.py和src_WH250801/position_5th.py两个文件，我希望后续所有的改建和升级都以这两个文件为核心。同时，可能有些功能已经在这个项目的其他代码中实现，有的需要你新实现。

第5版代码的具体要求如下，请先理解所有需求，从顶层设计代码架构，然后逐一具体实现：
1. 在采摘点位方法上，提供3个策略：①目标mask的几何中心；②目标mask区域内的最高点即depth深度值的最小点（比如src_WH250801/position_4th_highest.py中的实现）；③目标mask区域内的最缓点（最缓点的定义参考centerFind2.py中的safe_point相关代码，需要开启mariglod计算法线图）。采摘点位的可视化上，采摘点为红色实心点，如果启用策略③，则像centerFind2.py中那样将连通区域可视化
2. 在目标真实尺寸判断方法上（即计算其真实三维距离的两个点的选择上），提供3个策略：①过目标mask几何中心的最长的线段上的一对三分点（第4版的策略）；②【需要新实现】过目标mask区域的几何中心，做平行于x方向、平行于y方向、45°于x方向、45°于y方向的4条“星型”线段（即4条线段之间互相夹角45°），在这4条线段上各取一对三分点计算真实距离后再取平均；③【需要新实现】根据mask区域的像素面积，以几何中心为圆心拟合一个正圆形，并在此圆形的直径上取一对三分点计算其真实寸尺。尺寸判断的可视化上，线段为细白线，三分点为白色实心点。
3. 在采摘顺序规划方法上，提供3个策略：①根据所有目标的采摘点的真实深度，从低到高（第4版的策略）；②根据采摘点的像素位置，基于凸包解算和内角排序的方法，从外到内（参考vision_core.py中的“2.基于凸包算法从外到内”部分）；③根据所有目标mask的圆度，从圆到不圆（参考vision_core.py中的“3.基于圆度指标：已在主循环中实现”部分）。顺序规划的可视化上，用绿色数字绘制在采摘点附近。
4. 【需要新实现】在执行以上采摘顺序规划之前，是否（①②）先对所有mask执行位置上的聚类操作。如果增加聚类步骤，则根据簇内目标的数量，从少到多给簇排序，然后在各簇内正常执行以上采摘顺序规划。可视化上，如果不开启聚类，则照旧使用红色线绘制mask，如果开启聚类，则不同簇的目标要用不同的颜色绘制mask。
5. 是否（①②）使用marigold计算法线图。如果是，则单独保存法线图（参考hf_ND_marigold.py中的实现）。
6. 我希望用一个参数列表来控制这些策略的选取，比如[1,2,3,1,2]即代表使用几何中心点、星型直径、基于圆度的采摘顺序、要执行聚类步骤、不计算法线的策略组合。
7. 我希望计算yolov8模型和marigold模型（如果开启计算法线图）推理的用时、所有mask目标执行后处理功能的总用时，并为视觉系统的每一次启动都维护一个pandas表格，此表格的每一行都记录着imgname, yolo_latency, marigold_latency, postprocess_latency, target_info。其中target_info中包含此img所有目标的后处理结果信息，比如排序后的[x,y,z,D,X,Y,Z,α,β,γ],[x,y,z,D,X,Y,Z,α,β,γ],...其中xyz为采摘点像素坐标，D为真实尺寸，XYZ为采摘点真实三维坐标，αβγ为采摘点法线方向（如果开启计算法线的话）。在视觉系统终止前将此pandas表格保存为txt的log文件。
8. 视觉系统的其余部分比如串口通信、相机初始化、message构造等实现尽可能与第4版保持一致。