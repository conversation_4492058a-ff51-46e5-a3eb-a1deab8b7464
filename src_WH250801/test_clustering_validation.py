#!/usr/bin/env python3
"""
聚类功能验证测试脚本
用于验证修复后的聚类算法效果
"""

import csv
import json
import sys
import os
import math
import random

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def read_csv_data(csv_file_path):
    """
    读取CSV文件并提取target_info数据
    
    Args:
        csv_file_path: CSV文件路径
        
    Returns:
        list: 提取的目标信息列表
    """
    if not os.path.exists(csv_file_path):
        print(f"错误: CSV文件不存在 - {csv_file_path}")
        return None
    
    try:
        with open(csv_file_path, 'r', encoding='utf-8') as file:
            csv_reader = csv.DictReader(file)
            first_row = next(csv_reader)  # 读取第一行数据
            
            if 'target_info' not in first_row:
                print("错误: CSV文件中没有找到 'target_info' 字段")
                return None
            
            target_info_str = first_row['target_info']
            print(f"原始target_info字符串长度: {len(target_info_str)}")
            
            # 尝试解析JSON格式的target_info
            try:
                target_info = json.loads(target_info_str)
                print(f"成功解析JSON格式的target_info，包含{len(target_info)}个目标")
                return target_info
            except json.JSONDecodeError:
                print("target_info不是有效的JSON格式，尝试其他解析方法")
                return parse_target_info_alternative(target_info_str)
                
    except Exception as e:
        print(f"读取CSV文件时出错: {e}")
        return None

def parse_target_info_alternative(target_info_str):
    """
    备用的target_info解析方法
    """
    # 如果是字符串表示的列表格式，尝试解析
    try:
        # 移除可能的额外引号和空格
        cleaned_str = target_info_str.strip().strip('"').strip("'")
        target_info = eval(cleaned_str)  # 注意：eval有安全风险，仅用于测试
        print(f"使用备用方法解析target_info，包含{len(target_info)}个目标")
        return target_info
    except:
        print("无法解析target_info数据")
        return None

def extract_pixel_coordinates(target_info):
    """
    从target_info中提取像素坐标
    
    Args:
        target_info: 目标信息列表，支持两种格式：
                    - 字典格式: [{'pixel_x': x, 'pixel_y': y, ...}, ...]
                    - 列表格式: [[id, pixel_x, pixel_y, ...], ...]
        
    Returns:
        list: [(pixel_x, pixel_y), ...] 坐标列表
    """
    coordinates = []
    
    for i, target in enumerate(target_info):
        try:
            if isinstance(target, dict):
                # 字典格式处理
                pixel_x = target.get('pixel_x')
                pixel_y = target.get('pixel_y')
                
                if pixel_x is not None and pixel_y is not None:
                    coordinates.append((float(pixel_x), float(pixel_y)))
                else:
                    print(f"目标{i}缺少pixel_x或pixel_y字段")
                    
            elif isinstance(target, list) and len(target) >= 3:
                # 列表格式处理: [id, pixel_x, pixel_y, ...]
                # 根据实际数据格式，pixel_x在索引1，pixel_y在索引2
                pixel_x = target[1]
                pixel_y = target[2]
                
                coordinates.append((float(pixel_x), float(pixel_y)))
                
            else:
                print(f"目标{i}格式不支持: {type(target)}, 长度: {len(target) if isinstance(target, (list, tuple)) else 'N/A'}")
                
        except Exception as e:
            print(f"处理目标{i}时出错: {e}")
    
    print(f"成功提取{len(coordinates)}个有效坐标")
    return coordinates

def simple_kmeans(points, k=3, max_iterations=100):
    """
    简单的K-means聚类实现（不依赖sklearn）
    
    Args:
        points: 坐标点列表 [(x, y), ...]
        k: 聚类数量
        max_iterations: 最大迭代次数
        
    Returns:
        tuple: (cluster_labels, cluster_centers)
    """
    if len(points) <= k:
        return list(range(len(points))), points
    
    # 随机初始化聚类中心
    random.seed(42)  # 固定随机种子以获得可重复结果
    centers = random.sample(points, k)
    
    for iteration in range(max_iterations):
        # 分配每个点到最近的聚类中心
        labels = []
        for point in points:
            distances = [math.sqrt((point[0] - center[0])**2 + (point[1] - center[1])**2) 
                        for center in centers]
            labels.append(distances.index(min(distances)))
        
        # 更新聚类中心
        new_centers = []
        for cluster_id in range(k):
            cluster_points = [points[i] for i, label in enumerate(labels) if label == cluster_id]
            if cluster_points:
                center_x = sum(p[0] for p in cluster_points) / len(cluster_points)
                center_y = sum(p[1] for p in cluster_points) / len(cluster_points)
                new_centers.append((center_x, center_y))
            else:
                new_centers.append(centers[cluster_id])  # 保持原中心
        
        # 检查收敛
        if centers == new_centers:
            break
        centers = new_centers
    
    return labels, centers

def simple_dbscan(points, eps=35, min_samples=4):
    """
    简单的DBSCAN聚类实现（不依赖sklearn）
    
    Args:
        points: 坐标点列表 [(x, y), ...]
        eps: 邻域半径
        min_samples: 最小样本数
        
    Returns:
        list: cluster_labels (-1表示噪声点)
    """
    n_points = len(points)
    labels = [-2] * n_points  # -2表示未处理，-1表示噪声，>=0表示簇标签
    cluster_id = 0
    
    def get_neighbors(point_idx):
        """获取指定点的邻居"""
        neighbors = []
        for i, other_point in enumerate(points):
            if i != point_idx:
                distance = math.sqrt((points[point_idx][0] - other_point[0])**2 + 
                                   (points[point_idx][1] - other_point[1])**2)
                if distance <= eps:
                    neighbors.append(i)
        return neighbors
    
    for i in range(n_points):
        if labels[i] != -2:  # 已处理
            continue
            
        neighbors = get_neighbors(i)
        
        if len(neighbors) < min_samples:
            labels[i] = -1  # 标记为噪声点
        else:
            # 开始新簇
            labels[i] = cluster_id
            seed_set = neighbors[:]
            
            j = 0
            while j < len(seed_set):
                neighbor_idx = seed_set[j]
                
                if labels[neighbor_idx] == -1:  # 噪声点变为边界点
                    labels[neighbor_idx] = cluster_id
                elif labels[neighbor_idx] == -2:  # 未处理的点
                    labels[neighbor_idx] = cluster_id
                    neighbor_neighbors = get_neighbors(neighbor_idx)
                    
                    if len(neighbor_neighbors) >= min_samples:
                        seed_set.extend([n for n in neighbor_neighbors if n not in seed_set])
                
                j += 1
            
            cluster_id += 1
    
    return labels

def assign_noise_to_nearest_cluster(coordinates, labels):
    """
    将DBSCAN的噪声点分配给最近的簇
    
    Args:
        coordinates: 坐标列表
        labels: DBSCAN聚类标签
        
    Returns:
        list: 处理后的聚类标签（没有噪声点）
    """
    processed_labels = labels[:]
    
    # 找到所有簇的中心点
    unique_clusters = [l for l in set(labels) if l >= 0]
    if not unique_clusters:
        return processed_labels
    
    cluster_centers = {}
    for cluster_id in unique_clusters:
        cluster_points = [coordinates[i] for i, label in enumerate(labels) if label == cluster_id]
        if cluster_points:
            center_x = sum(p[0] for p in cluster_points) / len(cluster_points)
            center_y = sum(p[1] for p in cluster_points) / len(cluster_points)
            cluster_centers[cluster_id] = (center_x, center_y)
    
    # 为每个噪声点找到最近的簇
    for i, label in enumerate(labels):
        if label == -1:  # 噪声点
            point = coordinates[i]
            min_distance = float('inf')
            nearest_cluster = -1
            
            for cluster_id, center in cluster_centers.items():
                distance = math.sqrt((point[0] - center[0])**2 + (point[1] - center[1])**2)
                if distance < min_distance:
                    min_distance = distance
                    nearest_cluster = cluster_id
            
            if nearest_cluster != -1:
                processed_labels[i] = nearest_cluster
    
    return processed_labels

def create_visualization(coordinates, labels, algorithm_name, image_size=(848, 480)):
    """
    创建聚类结果的可视化图像（使用简单的文本输出）

    Args:
        coordinates: 坐标列表
        labels: 聚类标签
        algorithm_name: 算法名称
        image_size: 图像尺寸

    Returns:
        str: 可视化结果的文本描述
    """
    width, height = image_size

    # 定义颜色（用字符表示）
    colors = ['R', 'G', 'B', 'Y', 'M', 'C', 'W', 'K']  # 红绿蓝黄品青白黑

    # 创建简单的字符画板
    canvas = [['.' for _ in range(width // 10)] for _ in range(height // 10)]

    visualization_text = f"\n=== {algorithm_name} 聚类可视化 ===\n"
    visualization_text += f"图像尺寸: {width}x{height} (缩放到 {width//10}x{height//10} 显示)\n"
    visualization_text += "图例: . = 空白区域, R/G/B/Y/M/C = 不同簇, X = 噪声点\n\n"

    # 在画板上标记点
    for i, ((x, y), label) in enumerate(zip(coordinates, labels)):
        canvas_x = min(int(x // 10), width // 10 - 1)
        canvas_y = min(int(y // 10), height // 10 - 1)

        if label == -1:  # 噪声点
            canvas[canvas_y][canvas_x] = 'X'
        else:
            canvas[canvas_y][canvas_x] = colors[label % len(colors)]

    # 转换为文本
    for row in canvas:
        visualization_text += ''.join(row) + '\n'

    return visualization_text

def create_html_visualization(coordinates, labels, algorithm_name, filename):
    """
    创建HTML格式的可视化图像

    Args:
        coordinates: 坐标列表
        labels: 聚类标签
        algorithm_name: 算法名称
        filename: 输出文件名
    """
    # 定义颜色
    colors = ['#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF', '#FFA500', '#800080']

    html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>{algorithm_name} 聚类结果可视化</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .canvas {{
            border: 2px solid #333;
            position: relative;
            width: 848px;
            height: 480px;
            background-color: #f0f0f0;
            margin: 20px 0;
        }}
        .point {{
            position: absolute;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            border: 1px solid #000;
        }}
        .noise {{
            background-color: #888;
            width: 6px;
            height: 6px;
            transform: rotate(45deg);
            border-radius: 0;
        }}
        .legend {{
            margin: 10px 0;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }}
        .legend-item {{
            display: flex;
            align-items: center;
            gap: 5px;
        }}
        .legend-color {{
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 1px solid #000;
        }}
    </style>
</head>
<body>
    <h1>{algorithm_name} 聚类结果可视化</h1>
    <p>图像尺寸: 848x480 像素</p>
    <p>总目标数量: {len(coordinates)}</p>

    <div class="legend">
"""

    # 添加图例
    unique_labels = sorted(list(set(labels)))
    for label in unique_labels:
        if label == -1:
            html_content += f'<div class="legend-item"><div class="legend-color noise"></div><span>噪声点</span></div>'
        else:
            color = colors[label % len(colors)]
            count = labels.count(label)
            html_content += f'<div class="legend-item"><div class="legend-color" style="background-color: {color};"></div><span>簇 {label} ({count}个目标)</span></div>'

    html_content += """
    </div>

    <div class="canvas">
"""

    # 添加数据点
    for (x, y), label in zip(coordinates, labels):
        if label == -1:  # 噪声点
            html_content += f'<div class="point noise" style="left: {x-3}px; top: {y-3}px;"></div>'
        else:
            color = colors[label % len(colors)]
            html_content += f'<div class="point" style="left: {x-4}px; top: {y-4}px; background-color: {color};"></div>'

    html_content += """
    </div>

    <h2>聚类统计信息</h2>
    <table border="1" style="border-collapse: collapse;">
        <tr><th>簇ID</th><th>目标数量</th><th>颜色</th></tr>
"""

    # 添加统计表格
    for label in sorted([l for l in unique_labels if l >= 0]):
        count = labels.count(label)
        color = colors[label % len(colors)]
        html_content += f'<tr><td>簇 {label}</td><td>{count}</td><td style="background-color: {color}; width: 50px;">&nbsp;</td></tr>'

    if -1 in unique_labels:
        noise_count = labels.count(-1)
        html_content += f'<tr><td>噪声点</td><td>{noise_count}</td><td style="background-color: #888; width: 50px;">&nbsp;</td></tr>'

    html_content += """
    </table>
</body>
</html>
"""

    with open(filename, 'w', encoding='utf-8') as f:
        f.write(html_content)

    print(f"{algorithm_name} HTML可视化已保存到: {filename}")

def analyze_clustering_results(coordinates, labels, algorithm_name):
    """
    分析聚类结果
    
    Args:
        coordinates: 坐标列表
        labels: 聚类标签
        algorithm_name: 算法名称
    """
    print(f"\n=== {algorithm_name} 聚类结果分析 ===")
    
    # 统计簇信息
    unique_labels = list(set(labels))
    n_clusters = len([l for l in unique_labels if l >= 0])
    n_noise = labels.count(-1) if -1 in labels else 0
    
    print(f"簇数量: {n_clusters}")
    print(f"噪声点数量: {n_noise}")
    print(f"总目标数量: {len(coordinates)}")
    
    # 分析每个簇
    for cluster_id in sorted([l for l in unique_labels if l >= 0]):
        cluster_points = [coordinates[i] for i, label in enumerate(labels) if label == cluster_id]
        
        if cluster_points:
            # 计算簇的统计信息
            x_coords = [p[0] for p in cluster_points]
            y_coords = [p[1] for p in cluster_points]
            
            center_x = sum(x_coords) / len(x_coords)
            center_y = sum(y_coords) / len(y_coords)
            
            x_range = max(x_coords) - min(x_coords)
            y_range = max(y_coords) - min(y_coords)
            
            # 计算簇内平均距离
            total_distance = 0
            count = 0
            for i in range(len(cluster_points)):
                for j in range(i + 1, len(cluster_points)):
                    dist = math.sqrt((cluster_points[i][0] - cluster_points[j][0])**2 + 
                                   (cluster_points[i][1] - cluster_points[j][1])**2)
                    total_distance += dist
                    count += 1
            
            avg_distance = total_distance / count if count > 0 else 0
            
            print(f"\n簇 {cluster_id}:")
            print(f"  目标数量: {len(cluster_points)}")
            print(f"  中心位置: ({center_x:.1f}, {center_y:.1f})")
            print(f"  X坐标范围: {x_range:.1f} 像素")
            print(f"  Y坐标范围: {y_range:.1f} 像素")
            print(f"  簇内平均距离: {avg_distance:.1f} 像素")
            
            # 评估空间集中性
            if x_range < 100 and y_range < 100:
                print(f"  ✓ 空间集中（范围 < 100像素）")
            else:
                print(f"  ⚠ 空间分散（范围 >= 100像素）")

def find_csv_files():
    """查找可用的CSV文件"""
    possible_files = [
        "vision_performance_20250806_155311.csv",
        "vision_performance.csv",
        # 查找当前目录下的所有CSV文件
    ]

    # 添加当前目录下的所有CSV文件
    import glob
    csv_files = glob.glob("*.csv")
    possible_files.extend(csv_files)

    # 去重并返回存在的文件
    available_files = []
    for file in set(possible_files):
        if os.path.exists(file):
            available_files.append(file)

    return available_files

def main():
    """主函数"""
    print("=== 聚类功能验证测试 ===")

    # 1. 查找并读取CSV数据
    print("\n1. 查找CSV文件")
    csv_files = find_csv_files()

    target_info = None
    if csv_files:
        print(f"找到CSV文件: {csv_files}")
        for csv_file in csv_files:
            print(f"尝试读取: {csv_file}")
            target_info = read_csv_data(csv_file)
            if target_info:
                print(f"成功从 {csv_file} 读取数据")
                break

    if target_info is None:
        print("无法读取任何CSV数据，使用模拟数据进行测试")
        # 创建模拟数据
        target_info = create_mock_data()
    
    # 2. 提取像素坐标
    print("\n2. 提取像素坐标")
    coordinates = extract_pixel_coordinates(target_info)
    
    if not coordinates:
        print("没有有效的坐标数据")
        return
    
    print(f"坐标范围:")
    x_coords = [c[0] for c in coordinates]
    y_coords = [c[1] for c in coordinates]
    print(f"  X: {min(x_coords):.1f} - {max(x_coords):.1f}")
    print(f"  Y: {min(y_coords):.1f} - {max(y_coords):.1f}")
    
    # 3. K-means聚类测试
    print("\n3. K-means聚类测试")
    k = min(5, max(3, len(coordinates) // 12))  # 自动确定簇数量
    print(f"使用K-means聚类，k={k}")
    
    kmeans_labels, kmeans_centers = simple_kmeans(coordinates, k=k)
    analyze_clustering_results(coordinates, kmeans_labels, "K-means")
    
    # 4. DBSCAN聚类测试
    print("\n4. DBSCAN聚类测试")
    
    # 根据数据分布调整DBSCAN参数
    # 从K-means结果看，簇内平均距离约150-200像素
    # 我们尝试一个中等的eps值，既能形成多个簇，又不会产生太多噪声点
    eps = 70  # 设置为70像素的邻域半径
    min_samples = 3  # 最小样本数设为3
    print(f"使用DBSCAN聚类，eps={eps}, min_samples={min_samples}")
    
    dbscan_labels = simple_dbscan(coordinates, eps=eps, min_samples=min_samples)
    
    # 如果仍有噪声点且希望所有点都有簇归属，可以进行后处理
    print("\n4.1 DBSCAN后处理（为噪声点分配最近簇）")
    dbscan_labels_processed = assign_noise_to_nearest_cluster(coordinates, dbscan_labels)
    
    print(f"DBSCAN原始结果: {len(set([l for l in dbscan_labels if l >= 0]))}个簇, {dbscan_labels.count(-1)}个噪声点")
    print(f"DBSCAN后处理结果: {len(set([l for l in dbscan_labels_processed if l >= 0]))}个簇, {dbscan_labels_processed.count(-1)}个噪声点")
    analyze_clustering_results(coordinates, dbscan_labels, "DBSCAN")
    analyze_clustering_results(coordinates, dbscan_labels_processed, "DBSCAN (后处理)")
    
    # 5. 可视化输出
    print("\n5. 生成可视化结果")

    # 生成文本可视化
    kmeans_viz = create_visualization(coordinates, kmeans_labels, "K-means")
    dbscan_viz = create_visualization(coordinates, dbscan_labels, "DBSCAN")
    dbscan_processed_viz = create_visualization(coordinates, dbscan_labels_processed, "DBSCAN-后处理")

    # 保存文本可视化结果到文件
    with open("clustering_test_kmeans.txt", "w", encoding="utf-8") as f:
        f.write(kmeans_viz)
    print("K-means文本可视化结果已保存到: clustering_test_kmeans.txt")

    with open("clustering_test_dbscan.txt", "w", encoding="utf-8") as f:
        f.write(dbscan_viz)
    print("DBSCAN文本可视化结果已保存到: clustering_test_dbscan.txt")

    with open("clustering_test_dbscan_processed.txt", "w", encoding="utf-8") as f:
        f.write(dbscan_processed_viz)
    print("DBSCAN后处理文本可视化结果已保存到: clustering_test_dbscan_processed.txt")

    # 生成HTML可视化
    create_html_visualization(coordinates, kmeans_labels, "K-means", "clustering_test_kmeans.html")
    create_html_visualization(coordinates, dbscan_labels, "DBSCAN", "clustering_test_dbscan.html")
    create_html_visualization(coordinates, dbscan_labels_processed, "DBSCAN-后处理", "clustering_test_dbscan_processed.html")
    
    # 6. 总结评估
    print("\n6. 聚类效果总结")
    print("=" * 50)
    
    kmeans_clusters = len(set([l for l in kmeans_labels if l >= 0]))
    dbscan_clusters = len(set([l for l in dbscan_labels if l >= 0]))
    dbscan_processed_clusters = len(set([l for l in dbscan_labels_processed if l >= 0]))
    
    print(f"K-means: {kmeans_clusters}个簇")
    print(f"DBSCAN: {dbscan_clusters}个簇，{dbscan_labels.count(-1)}个噪声点")
    print(f"DBSCAN (后处理): {dbscan_processed_clusters}个簇，{dbscan_labels_processed.count(-1)}个噪声点")
    
    print("\n建议:")
    if kmeans_clusters >= 3:
        print("✓ K-means成功形成了至少3个簇，符合设计预期")
    else:
        print("⚠ K-means簇数量少于3个，可能需要调整参数")
    
    if dbscan_clusters >= 2:
        print("✓ DBSCAN成功识别了多个密度簇")
    else:
        print("⚠ DBSCAN可能需要调整eps或min_samples参数")
        
    if dbscan_labels_processed.count(-1) == 0:
        print("✓ DBSCAN后处理成功将所有点分配到簇中")

def create_mock_data():
    """创建模拟数据用于测试"""
    print("创建模拟蘑菇分布数据...")
    
    mock_data = []
    # 创建3个聚集区域
    centers = [(160, 120), (480, 120), (320, 360)]
    sizes = [15, 20, 15]
    
    target_id = 0
    for center, size in zip(centers, sizes):
        for i in range(size):
            x = center[0] + random.gauss(0, 30)
            y = center[1] + random.gauss(0, 30)
            
            # 确保坐标在图像范围内
            x = max(20, min(620, x))
            y = max(20, min(460, y))
            
            mock_data.append({
                'pixel_x': x,
                'pixel_y': y,
                'target_id': target_id,
                'size': 0.03 + random.random() * 0.02
            })
            target_id += 1
    
    return mock_data

if __name__ == "__main__":
    main()
