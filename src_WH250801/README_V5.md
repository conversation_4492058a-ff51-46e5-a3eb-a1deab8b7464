# 蘑菇采摘机器人视觉系统 第5版 (V5)

## 概述

第5版视觉系统是对第4版的完全重写和升级，主要目的是为视觉系统在对图片中每一个mask目标的采摘点选取、直径判定、采摘顺序规划和法线信息的输出与否等后处理功能上支持多种策略的实现及其快速切换和组合。

## 主要特性

### 1. 多策略架构
- **采摘点位选择策略 (3种)**
  - 几何中心策略：基于mask的几何中心计算采摘点
  - 最高点策略：选择深度最小（最近）的点作为采摘点
  - 最缓点策略：使用Marigold法线图找到最平缓的区域

- **尺寸判断策略 (3种)**
  - 最长线段策略：通过中心点的最长线段计算直径
  - 星型线段策略：4个方向（45°间隔）的线段平均长度
  - 圆形拟合策略：基于像素面积的圆形拟合

- **采摘顺序规划策略 (3种)**
  - 深度排序策略：按深度从近到远排序
  - 凸包算法策略：从外围到内部的路径规划
  - 圆度排序策略：从圆形到非圆形的优先级排序

### 2. 可选功能模块
- **位置聚类**：对相近的目标进行聚类分组
- **法线计算**：使用Marigold模型计算表面法线信息
- **性能监控**：详细的时间测量和数据记录

### 3. 参数化控制
- 策略参数格式：`[pick_strategy, size_strategy, order_strategy, clustering, normal_calc]`
- 支持配置文件和环境变量控制
- 实时策略切换和组合

## 文件结构

```
src_WH250801/
├── position_5th.py          # 核心视觉处理模块（第5版）
├── test_Run_5th.py          # 主程序入口（第5版）
├── test_v5_system.py        # 完整功能测试脚本
├── test_v5_simple.py        # 简化测试脚本
├── README_V5.md             # 本文档
├── position_4th.py          # 第4版模块（保留作为参考）
└── test_Run_4thnew.py       # 第4版主程序（保留作为参考）
```

## 核心类和模块

### 策略基类
- `PickingPointStrategy`: 采摘点位策略基类
- `SizeEstimationStrategy`: 尺寸判断策略基类  
- `SequencePlanningStrategy`: 顺序规划策略基类

### 具体策略实现
- `GeometricCenterStrategy`, `HighestPointStrategy`, `SafestPointStrategy`
- `LongestDiameterStrategy`, `StarShapedStrategy`, `CircleFittingStrategy`
- `DepthBasedStrategy`, `ConvexHullStrategy`, `CircularityBasedStrategy`

### 支持模块
- `StrategyController`: 策略控制和参数管理
- `PerformanceLogger`: 性能监控和数据记录
- `PositionClustering`: 位置聚类功能
- `NormalMapProcessor`: 法线图处理

## 使用方法

### 1. 策略参数配置

```python
# 策略参数格式: [pick_strategy, size_strategy, order_strategy, clustering, normal_calc]
# pick_strategy: 1=几何中心, 2=最高点, 3=最缓点
# size_strategy: 1=最长线段, 2=星型线段, 3=圆形拟合  
# order_strategy: 1=按深度, 2=凸包算法, 3=圆度排序
# clustering: 1=开启聚类, 2=关闭聚类
# normal_calc: 1=计算法线, 2=不计算法线

# 默认策略组合
DEFAULT_STRATEGY_PARAMS = [1, 1, 1, 2, 2]

# 高级策略组合示例
ADVANCED_STRATEGY_PARAMS = [3, 3, 2, 1, 1]  # 最缓点+圆形拟合+凸包+聚类+法线
```

### 2. 配置文件控制

创建配置文件 `/home/<USER>/Desktop/queryBattery/strategy_params.txt`:
```
2,1,3,1,2
```
这将设置为：最高点 + 最长线段 + 圆度排序 + 聚类 + 不计算法线

### 3. 主要API调用

```python
from position_5th import process_vision_v5, PerformanceLogger

# 初始化性能记录器
logger = PerformanceLogger(log_dir='/path/to/logs/')

# 处理图像
result_image, targets_info, processing_info = process_vision_v5(
    imgrgb=color_image,
    savePath=output_path,
    results=yolo_results,
    depth_frame=depth_frame,
    depth_intrin=depth_intrin,
    TSIZE=target_size_threshold,
    SIZEBIAS=size_bias,
    strategy_params=[1, 2, 3, 1, 2],  # 策略参数
    imgname=image_name,
    save_normal=True,  # 是否保存法线图
    logger=logger
)
```

## 性能监控

第5版系统包含完整的性能监控功能：

- **时间测量**：YOLO推理、Marigold法线计算、后处理各阶段时间
- **数据记录**：pandas格式的结构化数据记录
- **统计分析**：平均时间、目标数量统计等
- **日志保存**：CSV格式的详细日志文件

## 兼容性

- **向后兼容**：保持与第4版相同的输出格式，确保机器人控制系统无需修改
- **渐进升级**：可以逐步从第4版迁移到第5版
- **配置灵活**：支持运行时策略切换，无需重启系统

## 测试和验证

### 语法检查
```bash
python -m py_compile position_5th.py
python -m py_compile test_Run_5th.py
```

### 功能测试
```bash
# 简化测试（不需要外部依赖）
python test_v5_simple.py

# 完整功能测试（需要完整环境）
python test_v5_system.py
```

## 升级指南

### 从第4版升级到第5版

1. **保留第4版文件**作为备份
2. **复制核心文件**到第5版
3. **更新导入语句**：
   ```python
   # 第4版
   from position_4th import pickPoints
   
   # 第5版
   from position_5th import process_vision_v5, PerformanceLogger
   ```
4. **更新函数调用**：
   ```python
   # 第4版调用
   aft_dets = pickPoints(color_image, save_path, results, depth_frame, depth_intrin, TSIZE, SIZEBIAS, serial_n)
   
   # 第5版调用
   result_image, targets_info, processing_info = process_vision_v5(
       imgrgb=color_image, savePath=save_path, results=results, 
       depth_frame=depth_frame, depth_intrin=depth_intrin,
       TSIZE=TSIZE, SIZEBIAS=SIZEBIAS, strategy_params=STRATEGY_PARAMS,
       logger=performance_logger
   )
   ```
5. **配置策略参数**
6. **测试验证功能**

## 故障排除

### 常见问题

1. **导入错误**：确保所有依赖包已安装（pyrealsense2, torch, ultralytics等）
2. **策略参数错误**：检查参数范围是否正确（1-3）
3. **内存不足**：可以关闭法线计算功能（参数设为2）
4. **性能问题**：可以关闭聚类功能和法线计算

### 调试模式

设置环境变量启用详细日志：
```bash
export VISION_DEBUG=1
python test_Run_5th.py
```

## 未来扩展

第5版架构设计为高度可扩展：

- **新策略添加**：继承基类即可添加新的策略实现
- **参数优化**：支持更复杂的参数组合和优化
- **模型集成**：可以集成更多深度学习模型
- **实时调优**：支持运行时参数调整和策略切换

---

**版本信息**：V5.0  
**更新日期**：2025-08-03  
**兼容性**：Python 3.7+, PyTorch 1.8+, OpenCV 4.0+
