#!/usr/bin/env python3
"""
第5版蘑菇采摘机器人视觉系统简化测试脚本
测试核心策略逻辑，不依赖外部库
"""

import numpy as np
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_strategy_controller():
    """测试策略控制器"""
    print("=== 测试策略控制器 ===")
    
    try:
        from position_5th import StrategyController
        
        # 测试不同的策略参数组合
        test_params = [
            [1, 1, 1, 2, 2],  # 默认组合
            [2, 2, 2, 1, 2],  # 混合组合
            [3, 3, 3, 2, 1],  # 高级组合
        ]
        
        for i, params in enumerate(test_params):
            print(f"\n--- 测试策略组合 {i+1}: {params} ---")
            try:
                controller = StrategyController(params)
                description = controller.get_strategy_description()
                print(f"策略描述: {description['full_description']}")
                print("✓ 策略控制器初始化成功")
            except Exception as e:
                print(f"✗ 策略控制器初始化失败: {e}")
                
    except ImportError as e:
        print(f"✗ 导入策略控制器失败: {e}")

def test_picking_strategies():
    """测试采摘点位策略"""
    print("\n=== 测试采摘点位策略 ===")
    
    try:
        from position_5th import get_picking_point_strategy
        
        # 创建简单的测试mask
        mask = np.zeros((100, 100), dtype=bool)
        mask[30:70, 30:70] = True  # 40x40的方形区域
        
        print("测试mask创建成功，形状:", mask.shape, "True像素数:", np.sum(mask))
        
        for strategy_id in [1, 2, 3]:
            try:
                strategy = get_picking_point_strategy(strategy_id)
                strategy_name = {
                    1: "几何中心策略",
                    2: "最高点策略", 
                    3: "最缓点策略"
                }[strategy_id]
                print(f"✓ {strategy_name} 实例化成功")
            except Exception as e:
                print(f"✗ 策略 {strategy_id} 实例化失败: {e}")
                
    except ImportError as e:
        print(f"✗ 导入采摘点位策略失败: {e}")

def test_size_strategies():
    """测试尺寸判断策略"""
    print("\n=== 测试尺寸判断策略 ===")
    
    try:
        from position_5th import get_size_estimation_strategy
        
        for strategy_id in [1, 2, 3]:
            try:
                strategy = get_size_estimation_strategy(strategy_id)
                strategy_name = {
                    1: "最长线段策略",
                    2: "星型线段策略",
                    3: "圆形拟合策略"
                }[strategy_id]
                print(f"✓ {strategy_name} 实例化成功")
            except Exception as e:
                print(f"✗ 策略 {strategy_id} 实例化失败: {e}")
                
    except ImportError as e:
        print(f"✗ 导入尺寸判断策略失败: {e}")

def test_sequence_strategies():
    """测试顺序规划策略"""
    print("\n=== 测试顺序规划策略 ===")
    
    try:
        from position_5th import get_sequence_planning_strategy
        
        # 创建测试目标信息
        targets_info = [
            {'pixel_x': 100, 'pixel_y': 100, 'world_point': [0.1, 0.1, 0.3]},
            {'pixel_x': 200, 'pixel_y': 150, 'world_point': [0.2, 0.15, 0.25]},
            {'pixel_x': 150, 'pixel_y': 200, 'world_point': [0.15, 0.2, 0.35]},
        ]
        
        for strategy_id in [1, 2, 3]:
            try:
                strategy = get_sequence_planning_strategy(strategy_id)
                strategy_name = {
                    1: "深度排序策略",
                    2: "凸包算法策略",
                    3: "圆度排序策略"
                }[strategy_id]
                
                # 测试序列规划
                sequence = strategy.plan_sequence(targets_info)
                print(f"✓ {strategy_name} 执行成功，顺序: {sequence}")
                
            except Exception as e:
                print(f"✗ 策略 {strategy_id} 执行失败: {e}")
                
    except ImportError as e:
        print(f"✗ 导入顺序规划策略失败: {e}")

def test_clustering():
    """测试聚类功能"""
    print("\n=== 测试聚类功能 ===")
    
    try:
        from position_5th import PositionClustering
        
        # 创建测试目标信息
        targets_info = [
            {'pixel_x': 100, 'pixel_y': 100},
            {'pixel_x': 110, 'pixel_y': 105},  # 接近第一个点
            {'pixel_x': 300, 'pixel_y': 200},
            {'pixel_x': 310, 'pixel_y': 205},  # 接近第三个点
            {'pixel_x': 500, 'pixel_y': 300},  # 独立点
        ]
        
        try:
            clustering = PositionClustering()
            cluster_result = clustering.cluster_targets(targets_info)
            
            print(f"✓ 聚类执行成功")
            print(f"  聚类标签: {cluster_result['cluster_labels']}")
            print(f"  聚类中心数: {len(cluster_result['cluster_centers'])}")
            print(f"  聚类大小: {cluster_result['cluster_sizes']}")
            
        except Exception as e:
            print(f"✗ 聚类执行失败: {e}")
            
    except ImportError as e:
        print(f"✗ 导入聚类功能失败: {e}")

def test_performance_logger():
    """测试性能记录器"""
    print("\n=== 测试性能记录器 ===")
    
    try:
        from position_5th import PerformanceLogger
        
        try:
            # 创建临时日志目录
            import tempfile
            with tempfile.TemporaryDirectory() as temp_dir:
                logger = PerformanceLogger(log_dir=temp_dir)
                
                # 模拟一个处理会话
                logger.start_session("test_image.jpg")
                logger.record_yolo_time(0.1)
                logger.record_marigold_time(0.2)
                logger.record_postprocess_time(0.05)
                
                # 模拟目标信息
                targets_info = [
                    {'index': 0, 'pixel_x': 100, 'pixel_y': 100, 'size': 0.03}
                ]
                
                result = logger.end_session(targets_info, [1, 1, 1, 2, 2])
                
                print("✓ 性能记录器执行成功")
                print(f"  总处理时间: {result['total_latency']:.3f}s")
                print(f"  目标数量: {result['target_count']}")
                
        except Exception as e:
            print(f"✗ 性能记录器执行失败: {e}")
            
    except ImportError as e:
        print(f"✗ 导入性能记录器失败: {e}")

def test_imports():
    """测试所有模块导入"""
    print("=== 测试模块导入 ===")

    modules_to_test = [
        'StrategyController',
        'PerformanceLogger',
        'PositionClustering',
        'NormalMapProcessor',
        'get_picking_point_strategy',
        'get_size_estimation_strategy',
        'get_sequence_planning_strategy',
        'process_vision_v5'
    ]

    try:
        import position_5th

        for module_name in modules_to_test:
            try:
                module = getattr(position_5th, module_name, None)
                if module is not None:
                    print(f"✓ {module_name} 导入成功")
                else:
                    print(f"✗ {module_name} 未找到")
            except Exception as e:
                print(f"✗ {module_name} 导入失败: {e}")

    except Exception as e:
        print(f"✗ 整体导入失败: {e}")

def main():
    """主测试函数"""
    print("第5版蘑菇采摘机器人视觉系统简化测试")
    print("=" * 60)
    
    # 测试模块导入
    test_imports()
    
    # 测试各个组件
    test_strategy_controller()
    test_picking_strategies()
    test_size_strategies() 
    test_sequence_strategies()
    test_clustering()
    test_performance_logger()
    
    print("\n" + "=" * 60)
    print("测试完成！")

if __name__ == "__main__":
    main()
