from binascii import *
from crcmod import *
import time


# https://pypi.org/project/crcmod/
# pip install crcmod

# CRC16-MODBUS
def crc16Add(read):
    crc16 = crcmod.mkCrcFun(0x18005, rev=True, initCrc=0xFFFF, xorOut=0x0000)
    data = read.replace(" ", "")
    readcrcout = hex(crc16(unhexlify(data))) #.upper() 
    # print('readcrcout: ', readcrcout)
    str_list = list(readcrcout)
    # print('str_list: ', str_list)
    if len(str_list) < 6:  # ==5是为了当计算结果为5位时直接在之前补0
        zero = ''
        for i in range(6-len(str_list)):
            zero += '0'
        # print('zero：', zero)
        str_list.insert(2, zero)  # 位数不足补0
    crc_data = "".join(str_list)
    # print('crc_data: ', crc_data)
    #read = read.strip() + ' ' + crc_data[4:] + ' ' + crc_data[2:4]   #todo important line
    #print('CRC16校验:', crc_data[4:] + ' ' + crc_data[2:4])
    #print('增加Modbus_CRC16校验:>>>', read)4
    #return read            #todo important line
    #print('len of crc data: ', len(crc_data))
    # print('final result:', crc_data[4:] + crc_data[2:4])
    return crc_data[4:] + crc_data[2:4]
    #return crc_data[2:4] + crc_data[4:]


if __name__ == '__main__':
    #command = '01030000ff6f09ee01e601170a17ffcd02b80a32'  # 01030000ff7309ed01e201130a19ffc902b40a32  01030000ff6f09ee01e601170a17ffcd02b80a32
    #command = '01030004ff6f09ec01e601170a17ffc902b40a33'
    command = '0201067703550ae5'
    print(crc16Add(command))

