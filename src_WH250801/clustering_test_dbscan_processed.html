
<!DOCTYPE html>
<html>
<head>
    <title>DBSCAN-后处理 聚类结果可视化</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .canvas {
            border: 2px solid #333;
            position: relative;
            width: 848px;
            height: 480px;
            background-color: #f0f0f0;
            margin: 20px 0;
        }
        .point {
            position: absolute;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            border: 1px solid #000;
        }
        .noise {
            background-color: #888;
            width: 6px;
            height: 6px;
            transform: rotate(45deg);
            border-radius: 0;
        }
        .legend {
            margin: 10px 0;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 1px solid #000;
        }
    </style>
</head>
<body>
    <h1>DBSCAN-后处理 聚类结果可视化</h1>
    <p>图像尺寸: 848x480 像素</p>
    <p>总目标数量: 45</p>

    <div class="legend">
<div class="legend-item"><div class="legend-color" style="background-color: #FF0000;"></div><span>簇 0 (7个目标)</span></div><div class="legend-item"><div class="legend-color" style="background-color: #00FF00;"></div><span>簇 1 (17个目标)</span></div><div class="legend-item"><div class="legend-color" style="background-color: #0000FF;"></div><span>簇 2 (14个目标)</span></div><div class="legend-item"><div class="legend-color" style="background-color: #FFFF00;"></div><span>簇 3 (7个目标)</span></div>
    </div>

    <div class="canvas">
<div class="point" style="left: 400.0px; top: 337.0px; background-color: #0000FF;"></div><div class="point" style="left: 676.0px; top: 65.0px; background-color: #00FF00;"></div><div class="point" style="left: 344.0px; top: 435.0px; background-color: #0000FF;"></div><div class="point" style="left: 160.0px; top: 238.0px; background-color: #0000FF;"></div><div class="point" style="left: 391.0px; top: 233.0px; background-color: #0000FF;"></div><div class="point" style="left: 592.0px; top: 253.0px; background-color: #00FF00;"></div><div class="point" style="left: 476.0px; top: 428.0px; background-color: #0000FF;"></div><div class="point" style="left: 318.0px; top: 78.0px; background-color: #FF0000;"></div><div class="point" style="left: 136.0px; top: 313.0px; background-color: #0000FF;"></div><div class="point" style="left: 637.0px; top: 56.0px; background-color: #00FF00;"></div><div class="point" style="left: 391.0px; top: 418.0px; background-color: #0000FF;"></div><div class="point" style="left: 697.0px; top: 171.0px; background-color: #00FF00;"></div><div class="point" style="left: 603.0px; top: 339.0px; background-color: #00FF00;"></div><div class="point" style="left: 566.0px; top: 150.0px; background-color: #00FF00;"></div><div class="point" style="left: 501.0px; top: 191.0px; background-color: #FFFF00;"></div><div class="point" style="left: 71.0px; top: 157.0px; background-color: #FF0000;"></div><div class="point" style="left: 227.0px; top: 301.0px; background-color: #0000FF;"></div><div class="point" style="left: 789.0px; top: 336.0px; background-color: #00FF00;"></div><div class="point" style="left: 732.0px; top: 196.0px; background-color: #00FF00;"></div><div class="point" style="left: 499.0px; top: 366.0px; background-color: #0000FF;"></div><div class="point" style="left: 618.0px; top: 416.0px; background-color: #00FF00;"></div><div class="point" style="left: 310.0px; top: 17.0px; background-color: #FF0000;"></div><div class="point" style="left: 746.0px; top: 86.0px; background-color: #00FF00;"></div><div class="point" style="left: 141.0px; top: 80.0px; background-color: #FF0000;"></div><div class="point" style="left: 302.0px; top: 269.0px; background-color: #0000FF;"></div><div class="point" style="left: 387.0px; top: 52.0px; background-color: #FFFF00;"></div><div class="point" style="left: 465.0px; top: 28.0px; background-color: #FFFF00;"></div><div class="point" style="left: 281.0px; top: 56.0px; background-color: #FF0000;"></div><div class="point" style="left: 411.0px; top: 178.0px; background-color: #FFFF00;"></div><div class="point" style="left: 808.0px; top: 97.0px; background-color: #00FF00;"></div><div class="point" style="left: 518.0px; top: 65.0px; background-color: #FFFF00;"></div><div class="point" style="left: 791.0px; top: 206.0px; background-color: #00FF00;"></div><div class="point" style="left: 428.0px; top: 101.0px; background-color: #FFFF00;"></div><div class="point" style="left: 780.0px; top: 248.0px; background-color: #00FF00;"></div><div class="point" style="left: 617.0px; top: 106.0px; background-color: #00FF00;"></div><div class="point" style="left: 353.0px; top: 328.0px; background-color: #0000FF;"></div><div class="point" style="left: 87.0px; top: 249.0px; background-color: #0000FF;"></div><div class="point" style="left: 337.0px; top: 114.0px; background-color: #FF0000;"></div><div class="point" style="left: 286.0px; top: 338.0px; background-color: #0000FF;"></div><div class="point" style="left: 718.0px; top: 131.0px; background-color: #00FF00;"></div><div class="point" style="left: 222.0px; top: 43.0px; background-color: #FF0000;"></div><div class="point" style="left: 368.0px; top: 270.0px; background-color: #0000FF;"></div><div class="point" style="left: 684.0px; top: 216.0px; background-color: #00FF00;"></div><div class="point" style="left: 426.0px; top: 41.0px; background-color: #FFFF00;"></div><div class="point" style="left: 663.0px; top: 305.0px; background-color: #00FF00;"></div>
    </div>

    <h2>聚类统计信息</h2>
    <table border="1" style="border-collapse: collapse;">
        <tr><th>簇ID</th><th>目标数量</th><th>颜色</th></tr>
<tr><td>簇 0</td><td>7</td><td style="background-color: #FF0000; width: 50px;">&nbsp;</td></tr><tr><td>簇 1</td><td>17</td><td style="background-color: #00FF00; width: 50px;">&nbsp;</td></tr><tr><td>簇 2</td><td>14</td><td style="background-color: #0000FF; width: 50px;">&nbsp;</td></tr><tr><td>簇 3</td><td>7</td><td style="background-color: #FFFF00; width: 50px;">&nbsp;</td></tr>
    </table>
</body>
</html>
