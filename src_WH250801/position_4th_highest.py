import numpy as np 
import pyrealsense2 as rs
from scipy.ndimage import label, distance_transform_edt
from scipy.spatial.distance import cdist
import matplotlib.pyplot as plt
import matplotlib
import cv2
import torch
import math

'''
segmentation排序与路径规划。
原本针对食用菌二代，试图对采摘方向做路径规划：首先按坐标位置排序，然后寻找重叠蘑菇，对重叠蘑菇按照高度排序。（4.22）此进程仍有bug
食用菌三代增加了切槽和料槽，故路径规划上没有优化空间，则直接全部按照高度排序。（4.27）
yolov8-seg formattig.(2024/05/20)
'''
'''yolov8-seg <Results>
# Detection
result.boxes.xyxy   # box with xyxy format, (N, 4)
result.boxes.xywh   # box with xywh format, (N, 4)
result.boxes.xyxyn  # box with xyxy format but normalized, (N, 4)
result.boxes.xywhn  # box with xywh format but normalized, (N, 4)
result.boxes.conf   # confidence score, (N, 1)
result.boxes.cls    # cls, (N, 1)

# Classification
result.probs     # cls prob, (num_class, )

【250402】4th代码升级坐标处理方法
【250408】装备部署，根据new_position_seg.py中现行的必要功能修改此代码
【250414】实机调试
【250425】仅保留直径测算优化。V4.1
【250610】尝试将最高点作为采摘点
'''

BIAS = 0
BIAS_MAXX = 7
BIAS_MAXY = 5
RSLUX = 848
RSLUY = 480
imgCenter = [RSLUX, RSLUY]


def find_mask_contours(mask):
    """找到mask的轮廓"""
    mask_uint8 = mask.astype(np.uint8) * 255
    contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:  # 如果没有找到轮廓
        return [], (0, 0)
    
    # 找到最大的轮廓
    max_contour = max(contours, key=cv2.contourArea)
    
    # 计算最大轮廓的几何中心
    M = cv2.moments(max_contour)
    if M["m00"] > 0:  # 确保轮廓的面积大于0
        cx = int(M["m10"] / M["m00"])
        cy = int(M["m01"] / M["m00"])
        center = (cx, cy)
    else:
        center = (0, 0)
    
    return contours, center

def find_mask_diameter(mask, sample_ratio=0.5):
    """
    寻找经过mask几何中心且长度最大的直径
    
    Args:
        mask: 2D布尔数组，表示mask区域
    
    Returns:
        start_point: 直径的起点坐标 (x, y)
        end_point: 直径的终点坐标 (x, y)
    """
    # 确保mask是布尔类型
    mask = mask.astype(np.uint8)
    
    # 计算mask的几何中心（质心）
    M = cv2.moments(mask)
    if M["m00"] == 0:
        return None, None  # 空mask
    center_x = int(M["m10"] / M["m00"])
    center_y = int(M["m01"] / M["m00"])
    center = (center_x, center_y)
    
    # 找到mask的轮廓
    contours, _ = find_mask_contours(mask)
    contours = [max(contours, key=cv2.contourArea)]
    if not contours:
        return None, None
    
    # 合并所有轮廓点并按固定间隔采样以减少计算量
    all_contour_points = []
    for contour in contours:
        # 计算采样步长，确保至少取2个点
        step = max(1, int(1 / sample_ratio))
        sampled_points = [point[0] for point in contour[::step]]
        all_contour_points.extend(sampled_points)
    
    # 如果轮廓点太少，返回None
    if len(all_contour_points) < 2:
        return None, None
    
    # 寻找经过或接近中心的最长线段
    max_distance = 0
    best_pair = None
    
    # 设置接近中心的阈值（线到中心点的最大距离）
    threshold = max(5, np.sqrt(mask.shape[0]**2 + mask.shape[1]**2) * 0.05)  # 5或图像对角线的5%
    
    # 对每一对轮廓点检查
    n = len(all_contour_points)
    for i in range(n):
        for j in range(i+1, n):
            p1 = all_contour_points[i]
            p2 = all_contour_points[j]
            
            # 计算点到线的距离
            # 线段方程: (x-x1)/(x2-x1) = (y-y1)/(y2-y1)
            # 距离公式: |Ax + By + C|/sqrt(A^2 + B^2)，其中线的方程为Ax + By + C = 0
            
            if p1[0] == p2[0]:  # 垂直线
                distance_to_center = abs(center_x - p1[0])
            elif p1[1] == p2[1]:  # 水平线
                distance_to_center = abs(center_y - p1[1])
            else:
                # 一般情况
                A = (p2[1] - p1[1])
                B = (p1[0] - p2[0])
                C = (p2[0]*p1[1] - p1[0]*p2[1])
                distance_to_center = abs(A*center_x + B*center_y + C) / np.sqrt(A**2 + B**2)
            
            # 如果线段经过或接近中心，计算长度
            if distance_to_center <= threshold:
                # 计算线段长度
                distance = np.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
                
                if distance > max_distance:
                    max_distance = distance
                    best_pair = (p1, p2)
    
    if best_pair is None:
        return None, None, None, None, None
    
    p1, p2 = best_pair
    
    # 计算三等分点
    # 使用参数方程：P = P1 + t(P2-P1)，其中t分别为1/3和2/3
    third_point1 = (
        int(p1[0] + (p2[0] - p1[0]) / 3),  # x坐标
        int(p1[1] + (p2[1] - p1[1]) / 3)   # y坐标
    )
    
    third_point2 = (
        int(p1[0] + 2 * (p2[0] - p1[0]) / 3),  # x坐标
        int(p1[1] + 2 * (p2[1] - p1[1]) / 3)   # y坐标
    )
    
    return best_pair[0], best_pair[1], center, third_point1, third_point2, contours


def shrink_mask(mask, shrink_pixels=10):
    """
    根据给定的像素数缩小mask边界，生成位于目标中间的候选区域。
    
    :param mask: 输入的原始mask，形状为[H, W]的二维数组（支持bool或二值化0/1）。
    :param shrink_pixels: 每边缩小的像素数量，默认为10。
    :return: 缩小后的候选区域mask。
    """
    # 确保mask是numpy数组，并转换为布尔类型
    mask = mask.astype(bool)

    if shrink_pixels < 0 or shrink_pixels > min(mask.shape[0], mask.shape[1]) // 2:
        raise ValueError("shrink_pixels should be positive and less than half of the smallest dimension of the mask.")

    # 找到mask的边界
    rows = np.any(mask, axis=1)
    cols = np.any(mask, axis=0)
    rmin, rmax = np.where(rows)[0][[0, -1]]
    cmin, cmax = np.where(cols)[0][[0, -1]]

    # 计算新的边界
    new_rmin = rmin + shrink_pixels
    new_rmax = rmax - shrink_pixels
    new_cmin = cmin + shrink_pixels
    new_cmax = cmax - shrink_pixels

    # 边界检查
    if new_rmin >= new_rmax or new_cmin >= new_cmax:
        return np.zeros_like(mask)  # 如果缩得太小，返回空mask

    # 创建缩小后的矩形区域
    shrunk_mask = np.zeros_like(mask, dtype=bool)
    shrunk_mask[new_rmin:new_rmax+1, new_cmin:new_cmax+1] = True

    # 与原始mask交集，保证只保留mask内部区域
    shrunk_mask = shrunk_mask & mask

    return shrunk_mask

# 【250402】采摘点计算，坐标排序与路径规划
def pickPoints(imgrgb, savePath, results, depth_frame, depth_intrin, TSIZE, SIZEBIAS, serial_n):
    '''
    # Segmentation
    result.masks.data      # masks, (N, H, W)
    result.masks.xy        # x,y segments (pixels), List[segment] * N
    result.masks.xyn       # x,y segments (normalized), List[segment] * N
    '''
    # normal_map = np.array(normals)
    masks = results.masks.data if results.masks is not None else []
    detections = results.boxes.xywh
    print("-----Detected {:d} mushroom in image-----\n".format(detections.size()[0]))
    dets = []
    minus = [0, 0, 0, 0]  # pickable, pixel size, w/h ratio, real size
    
    # 创建结果图像
    result_image = imgrgb.copy()
    
    # 创建热力图颜色映射
    colormap = matplotlib.colormaps.get_cmap('jet')
    
    # 对每个检测到的实例进行处理
    for i, mask_tensor in enumerate(masks):
        # get info of detections
        detection = detections[i]

        # 基于像素的长宽比判断
        if detection[2]/detection[3]>1.3 or detection[2]/detection[3]<0.7:
            minus[2] += 1
            continue
        
        # Process pick points now
        
        # 转换为numpy数组
        mask = mask_tensor.cpu().numpy()
        # 获取单个mask的坐标，并缩小边界
        shrunk_mask = shrink_mask(mask, shrink_pixels=10)

        # 找到mask的质心和经过几何中心的最长直径
        start_point, end_point, center, diameterP1, diameterP2, contours = find_mask_diameter(mask)

        # 【250610】直接在此处增量式替换原有center点
        # 获取单个mask的坐标
        mask_coords = np.argwhere(shrunk_mask)
        if mask_coords.size == 0:
            continue
        # 根据mask坐标提取对应的深度值
        depths = np.array([depth_frame.get_distance(x, y) for y, x in mask_coords])
        # 找到深度最小值的点
        max_depth_idx = np.argmin(depths)
        center_high = mask_coords[max_depth_idx]
        # 计算真实世界坐标
        depth_high = depths[max_depth_idx]
        center_high_point = rs.rs2_deproject_pixel_to_point(depth_intrin, [center_high[1], center_high[0]], depth_high)
        

        cv2.drawContours(result_image, contours, -1, (0, 0, 255), 2)
        if start_point is not None and end_point is not None:
            # 顺便把几何中心绘制出来
            cv2.circle(result_image, center, 3, (0, 0, 255), -1)  # 红色实心点
            # 顺便把最高点绘制出来
            cv2.circle(result_image, (int(center_high[1]), int(center_high[0])), 3, (255, 255, 0), -1)  # 黄色实心点
            # 绘制直径线段（白色，粗细为2）
            cv2.line(result_image, start_point, end_point, (255, 255, 255), 1)
            # 把两个测量点绘制出来
            cv2.circle(result_image, diameterP1, 3, (0, 255, 0), -1)  # 绿色实心点
            cv2.circle(result_image, diameterP2, 3, (0, 255, 0), -1)  # 绿色实心点
        
        # 检测采摘点的深度值是否存在判断
        pick_depth = depth_frame.get_distance(int(center[0]), int(center[1]))
        if pick_depth == 0:  # 如果深度信息为空
            print('=====NULL depth info')
            minus[0] += 1
            continue
            # print('=====NULL depth info at ({:.0f},{:.0f}) and set to 0.245====='.format(safe_x.cpu().numpy(), safe_y.cpu().numpy()))
            # pick_depth = 0.2450

        rf1_depth = depth_frame.get_distance(int(diameterP1[0]), int(diameterP1[1]))
        rf2_depth = depth_frame.get_distance(int(diameterP2[0]), int(diameterP2[1]))
        rf1_point = rs.rs2_deproject_pixel_to_point(depth_intrin, [int(diameterP1[0]), int(diameterP1[1])], rf1_depth)
        rf2_point = rs.rs2_deproject_pixel_to_point(depth_intrin, [int(diameterP2[0]), int(diameterP2[1])], rf2_depth)
        
        # calculate the diameter of two rf points
        size = math.sqrt(sum((a-b) ** 2 for a, b in zip(rf1_point, rf2_point))) *3 + SIZEBIAS
        
        if size < TSIZE:
            print('=====Diameter too small: {:.1f}\n'.format(size*1000))
            minus[3] += 1
            continue
        print('-----Diameter: {:.1f}\n'.format(size*1000))
        # put size text
        labelsize = f"s{size*1000:.1f}"
        possize = (int(center[0]-20), int(center[1]+22))
        cv2.putText(result_image, labelsize,
                    possize,
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.4,  # font scale
                    (0, 255, 0),  # 绿色指示尺寸
                    1)  # line type

        # 获取采摘点的深度坐标 picking point!
        pick_point = rs.rs2_deproject_pixel_to_point(depth_intrin, [int(center[0]), int(center[1])], pick_depth)
        # put depth text
        labeldepth = f"d{pick_point[2]*1000:.1f}"
        posdepth = (int(center[0]-20), int(center[1]-3))
        cv2.putText(result_image, labeldepth,
                    posdepth,
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.4,  # font scale
                    (0, 0, 255),  # 红色指示深度
                    1)  # line type

        # grab useful info from processed results
        # dets.append([pick_point[0], pick_point[1], pick_point[2], 0.0, 0.0, 0.0, size])  # 几何中心做采摘点
        dets.append([center_high_point[0], center_high_point[1], center_high_point[2], 0.0, 0.0, 0.0, size])  # 最高点做采摘点

    print('=====Depreciated Depth holl, Pixel size, W/H ratio, Real size: ', minus)
    # 按照位置信息从上到下和从下到上
    aft_dets = sorted(dets, key=lambda d: d[2])  # 按照top值从小到大排序，双臂则增加一种从大到小
    print('-----Output {} objects in image'.format(len(aft_dets)))

    cv2.imwrite(savePath, result_image)

    return aft_dets