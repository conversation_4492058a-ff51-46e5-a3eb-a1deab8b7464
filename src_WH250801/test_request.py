import requests
import cv2
from datetime import datetime


def harvestCount_test():
    '''
    图片及识别蘑菇数量的传输接口
    调用时间：采摘前识别和采摘后
    photo，图像
    harvestCount，蘑菇识别数量
    flag，一共 2 个摄像头，用 0/1区分
    photoTime，图像拍摄时间。暂时要求没那么严格。
    '''
    # 请求地址
    harvestCount_url = 'http://robot.tztechnology.cn/transmitData'

    # 演示已经用 opencv 正在处理的图像如何传输
    img = cv2.imread('/Users/<USER>/Downloads/231715393124_.pic.jpg')
    _, img_encoded = cv2.imencode('.jpg', img)
    image = {'photo': ('image.jpg', img_encoded.tobytes(), 'image/jpeg', {'Expires': '0'})}

    # 演示数据部分传输
    photo_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    data = {'harvestCount': 10, 'flag': 1, 'photoTime': photo_time}

    # 请求
    response = requests.post(harvestCount_url, files=image, data=data)

    # 响应，如果 code==200，则成功
    print(response.text)


def harvestDuration_test():
    '''
    平均采摘时长的传输接口
    调用时间：有采摘数据后就可以传输，与另一个接口无时序关系
    harvestDuration，平均采摘时长，
    sendTime，发送时间，这个暂时用当前时间即可。预留给后期开发本地临时存储。
    '''
    # 请求地址
    harvestDuration_url = 'http://robot.tztechnology.cn/transmitData/time'

    # 演示数据部分传输
    send_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    data = {'harvestDuration': 100, 'flag': 1, 'sendTime': send_time}

    # 请求
    response = requests.post(harvestDuration_url, data=data)

    # 响应，如果 code==200，则成功
    print(response.text)


if __name__ == '__main__':
    harvestDuration_test()
