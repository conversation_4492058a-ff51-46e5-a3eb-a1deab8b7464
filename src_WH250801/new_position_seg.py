import numpy as np 
import pyrealsense2 as rs
'''
segmentation排序与路径规划。
原本针对食用菌二代，试图对采摘方向做路径规划：首先按坐标位置排序，然后寻找重叠蘑菇，对重叠蘑菇按照高度排序。（4.22）此进程仍有bug
食用菌三代增加了切槽和料槽，故路径规划上没有优化空间，则直接全部按照高度排序。（4.27）
yolov8-seg formattig.(2024/05/20)
'''
'''yolov8-seg <Results>
# Detection
result.boxes.xyxy   # box with xyxy format, (N, 4)
result.boxes.xywh   # box with xywh format, (N, 4)
result.boxes.xyxyn  # box with xyxy format but normalized, (N, 4)
result.boxes.xywhn  # box with xywh format but normalized, (N, 4)
result.boxes.conf   # confidence score, (N, 1)
result.boxes.cls    # cls, (N, 1)

# Segmentation
result.masks.data      # masks, (N, H, W)
result.masks.xy        # x,y segments (pixels), List[segment] * N
result.masks.xyn       # x,y segments (normalized), List[segment] * N

# Classification
result.probs     # cls prob, (num_class, )
'''

BIAS = 0
BIAS_MAXX = 7
BIAS_MAXY = 5
RSLUX = 848
RSLUY = 480
imgCenter = [RSLUX, RSLUY]


# 判断某框的中心点是否在某框周围区域
def Judge(t_det, det_around):
    if (t_det[0]>det_around[0] and t_det[0]<det_around[2] and
        t_det[1]>det_around[1] and t_det[1]<det_around[3]):
        return 1


# 查找某框是否与其他框重叠，并排序
def FindAndSort(t_det, bef_dets):
    Find = [t_det]  # 重叠的检测框组，至少有本框一个
    left = max(t_det[0]-t_det[2], 0)  # 
    top = max(t_det[1]-t_det[3], 0)
    right = min(t_det[0]+t_det[2], RSLUX)
    bottom = min(t_det[1]+t_det[3], RSLUY)
    det_around = [left, top, right, bottom]  # 本框周围区域
    for i in range(len(bef_dets)):
        #如果此框尚未参与排序，而且此框处于区域内
        if bef_dets[i][7] == 0 and Judge(bef_dets[i], det_around):  # 如果一个框未曾参与过排序而且在本框的周围区域中
            bef_dets[i][7] = 1
            Find.append(bef_dets[i])
    print('\nFind list: ', len(Find), Find)
    print('\nflaged bef_dets: \n', len(bef_dets), bef_dets)
    return bef_dets, sorted(Find, key=lambda h: h[6], reverse=True)  # 按照高度排序，离摄像头近的，即数值小的排在前面


# 坐标排序与路径规划
def positionTrans(result, depth_frame, depth_intrin, TSIZE, serial_n):
    detections = result.boxes.xywh
    print("\n-----Detected {:d} mushroom in image-----".format(detections.size()[0]))
    dets = []
    minus = [0, 0, 0, 0]
    for i in range(detections.size()[0]):
        # tensor([524.9587,   239.3336,  85.5627,  80.6733], device='cuda:0')
        #        x(640/848), y(416/480),  w,      h 
        #           [0],      [1],       [2],     [3]
        detection = detections[i]
        
        # filter based on box position for UI demo;
        # left arm:  240~610
        # right arm: 315~675
        # if serial_n == 1:  
        #     if detection[0] < 240 or detection[0] > 610 or detection[1] < 30 or detection[1] > 450:
        #         minus[0] += 1
        #         continue
        # else:
        #     if detection[0] < 315 or detection[0] > 675 or detection[1] < 30 or detection[1] > 450:
        #         minus[0] += 1
        #         continue
        
        # filter based on box pixel size; 40mm mushroom model: 90 pixel
        # if detection[2] < 40 or detection[3] < 40:
        #     minus[1] += 1
        #     continue

        # 基于像素的长宽比判断
        if detection[2]/detection[3]>1.3 or detection[2]/detection[3]<0.7:
            minus[2] += 1
            continue
        
        # 修正检测框中心点的坐标偏移
        center_x = detection[0]
        center_y = detection[1]
        if BIAS:
            center_x += (center_x-imgCenter[0]) / int(RSLUX/2) * BIAS_MAXX
            center_y += (center_y-imgCenter[1]) / int(RSLUY/2) * BIAS_MAXY
        
        # 检测框中心点的深度值是否存在判断
        center_depth = depth_frame.get_distance(int(center_x), int(center_y))
        # print('center_depth: ', center_depth)
        if center_depth == 0:  # 如果深度信息为空
            print('=====NULL depth info at ({:.0f},{:.0f}) and set to 0.245====='.format(
            	int(center_x.cpu().numpy()),
            	int(center_y.cpu().numpy())
            	))
            center_depth = 0.2450
            # minus[1] += 1
            # continue

        # 采摘点y方向上取两点，按照两点真实距离的二倍
        rf_x = center_x
        rf1_y = (center_y - detection[3]/2 + center_y)/2  # up point
        rf2_y = (center_y + detection[3]/2 + center_y)/2  # down point
        # print('-----size measurement point: x-{:.0f}, y-{:.0f},{:.0f},{:.0f}'.format(rf_x, rf1_y, rf2_y, center_y))
        rf1_depth = depth_frame.get_distance(int(rf_x), int(rf1_y))
        rf2_depth = depth_frame.get_distance(int(rf_x), int(rf2_y))
        rf1_point = rs.rs2_deproject_pixel_to_point(depth_intrin, [int(rf_x), int(rf1_y)], rf1_depth)
        rf2_point = rs.rs2_deproject_pixel_to_point(depth_intrin, [int(rf_x), int(rf2_y)], rf2_depth)
        # print('reference points x:      ', rf2_point[0]-rf1_point[0])
        # print('reference1 and center x: ', center_point[0]-rf1_point[0])
        # print('center x and reference2: ', rf2_point[0]-center_point[0])
        size = (rf2_point[1] - rf1_point[1])*2
        if size < TSIZE:
            print('=====diameter too small: {:.1f}\n'.format(size*1000))
            minus[3] += 1
            continue
        print('-----diameter: {:.1f}\n'.format(size*1000))

         # 获取中心的深度坐标 picking point!
        center_point = rs.rs2_deproject_pixel_to_point(depth_intrin, [int(center_x), int(center_y)], center_depth)
        # print('\ncenter_point: ', center_point)

        # 从detection类中构建有用信息的list
        dets.append([center_x, center_y, detection[2], detection[3], center_point[0], center_point[1], center_point[2], size])

    print('=====Position limit, Pixel size, W/H ratio, Real size minus: ', minus)
    # 按照位置信息从上到下和从下到上
    aft_dets = sorted(dets, key=lambda d: d[6])  # 按照top值从小到大排序， 双臂则增加一种从大到小
    # aft_dets = dets
    print('-----Output {} objects in image'.format(len(aft_dets)))

    return aft_dets
