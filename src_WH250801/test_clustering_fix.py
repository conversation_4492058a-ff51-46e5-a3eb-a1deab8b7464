#!/usr/bin/env python3
"""
测试聚类功能修复效果的脚本
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from position_5th import KMeansClusteringStrategy, DBSCANClusteringStrategy, draw_masks_on_image
import random

def create_test_targets(n_targets=50):
    """创建测试目标数据，模拟蘑菇在640x480图像中的分布"""
    targets_info = []
    
    # 创建3个聚集区域，模拟蘑菇的自然分布
    cluster_centers = [
        (160, 120),  # 左上区域
        (480, 120),  # 右上区域  
        (320, 360),  # 下方中央区域
    ]
    
    cluster_sizes = [15, 20, 15]  # 每个区域的目标数量
    
    target_id = 0
    for cluster_id, (center_x, center_y) in enumerate(cluster_centers):
        n_in_cluster = cluster_sizes[cluster_id]
        
        for i in range(n_in_cluster):
            # 在聚类中心周围随机分布
            offset_x = random.gauss(0, 40)  # 标准差40像素
            offset_y = random.gauss(0, 40)
            
            pixel_x = max(20, min(620, center_x + offset_x))
            pixel_y = max(20, min(460, center_y + offset_y))
            
            # 创建简单的圆形mask
            mask = np.zeros((480, 640), dtype=np.uint8)
            radius = random.randint(15, 25)
            cv2.circle(mask, (int(pixel_x), int(pixel_y)), radius, 255, -1)
            
            target = {
                'pixel_x': pixel_x,
                'pixel_y': pixel_y,
                'mask': mask,
                'size': 0.03 + random.random() * 0.02,  # 0.03-0.05m
                'target_id': target_id
            }
            targets_info.append(target)
            target_id += 1
    
    return targets_info

def test_clustering_algorithms():
    """测试聚类算法"""
    print("=== 聚类功能修复测试 ===")
    
    # 创建测试数据
    targets_info = create_test_targets(50)
    print(f"创建了{len(targets_info)}个测试目标")
    
    # 创建测试图像
    test_image = np.ones((480, 640, 3), dtype=np.uint8) * 255  # 白色背景
    
    # 测试K-means聚类
    print("\n--- 测试K-means聚类 ---")
    kmeans_strategy = KMeansClusteringStrategy()
    kmeans_result = kmeans_strategy.cluster_targets(targets_info)
    
    if kmeans_result:
        print(f"K-means聚类结果:")
        print(f"  算法: {kmeans_result.get('algorithm', 'Unknown')}")
        print(f"  簇数量: {len(kmeans_result.get('sorted_clusters', []))}")
        print(f"  簇大小: {kmeans_result.get('cluster_sizes', [])}")
        
        # 可视化K-means结果
        kmeans_image = draw_masks_on_image(test_image.copy(), targets_info, kmeans_result)
        cv2.imwrite('test_kmeans_result.jpg', cv2.cvtColor(kmeans_image, cv2.COLOR_RGB2BGR))
        print("  K-means可视化结果已保存: test_kmeans_result.jpg")
    
    # 测试DBSCAN聚类
    print("\n--- 测试DBSCAN聚类 ---")
    dbscan_strategy = DBSCANClusteringStrategy()
    dbscan_result = dbscan_strategy.cluster_targets(targets_info)
    
    if dbscan_result:
        print(f"DBSCAN聚类结果:")
        print(f"  算法: {dbscan_result.get('algorithm', 'Unknown')}")
        print(f"  簇数量: {len(dbscan_result.get('sorted_clusters', []))}")
        print(f"  簇大小: {dbscan_result.get('cluster_sizes', [])}")
        print(f"  噪声点数量: {len(dbscan_result.get('noise_points', []))}")
        
        # 可视化DBSCAN结果
        dbscan_image = draw_masks_on_image(test_image.copy(), targets_info, dbscan_result)
        cv2.imwrite('test_dbscan_result.jpg', cv2.cvtColor(dbscan_image, cv2.COLOR_RGB2BGR))
        print("  DBSCAN可视化结果已保存: test_dbscan_result.jpg")
    
    # 测试无聚类情况
    print("\n--- 测试无聚类情况 ---")
    no_cluster_image = draw_masks_on_image(test_image.copy(), targets_info, None)
    cv2.imwrite('test_no_cluster_result.jpg', cv2.cvtColor(no_cluster_image, cv2.COLOR_RGB2BGR))
    print("  无聚类可视化结果已保存: test_no_cluster_result.jpg")
    
    print("\n=== 测试完成 ===")
    print("请检查生成的图像文件，验证聚类颜色是否正确分配")

if __name__ == "__main__":
    test_clustering_algorithms()
