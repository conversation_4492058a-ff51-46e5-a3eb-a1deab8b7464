
<!DOCTYPE html>
<html>
<head>
    <title>DBSCAN 聚类结果可视化</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .canvas {
            border: 2px solid #333;
            position: relative;
            width: 848px;
            height: 480px;
            background-color: #f0f0f0;
            margin: 20px 0;
        }
        .point {
            position: absolute;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            border: 1px solid #000;
        }
        .noise {
            background-color: #888;
            width: 6px;
            height: 6px;
            transform: rotate(45deg);
            border-radius: 0;
        }
        .legend {
            margin: 10px 0;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 1px solid #000;
        }
    </style>
</head>
<body>
    <h1>DBSCAN 聚类结果可视化</h1>
    <p>图像尺寸: 848x480 像素</p>
    <p>总目标数量: 45</p>

    <div class="legend">
<div class="legend-item"><div class="legend-color noise"></div><span>噪声点</span></div><div class="legend-item"><div class="legend-color" style="background-color: #FF0000;"></div><span>簇 0 (5个目标)</span></div><div class="legend-item"><div class="legend-color" style="background-color: #00FF00;"></div><span>簇 1 (6个目标)</span></div><div class="legend-item"><div class="legend-color" style="background-color: #0000FF;"></div><span>簇 2 (6个目标)</span></div><div class="legend-item"><div class="legend-color" style="background-color: #FFFF00;"></div><span>簇 3 (4个目标)</span></div>
    </div>

    <div class="canvas">
<div class="point" style="left: 400.0px; top: 337.0px; background-color: #0000FF;"></div><div class="point noise" style="left: 677.0px; top: 66.0px;"></div><div class="point noise" style="left: 345.0px; top: 436.0px;"></div><div class="point noise" style="left: 161.0px; top: 239.0px;"></div><div class="point" style="left: 391.0px; top: 233.0px; background-color: #0000FF;"></div><div class="point noise" style="left: 593.0px; top: 254.0px;"></div><div class="point noise" style="left: 477.0px; top: 429.0px;"></div><div class="point" style="left: 318.0px; top: 78.0px; background-color: #FF0000;"></div><div class="point noise" style="left: 137.0px; top: 314.0px;"></div><div class="point noise" style="left: 638.0px; top: 57.0px;"></div><div class="point noise" style="left: 392.0px; top: 419.0px;"></div><div class="point" style="left: 697.0px; top: 171.0px; background-color: #00FF00;"></div><div class="point noise" style="left: 604.0px; top: 340.0px;"></div><div class="point noise" style="left: 567.0px; top: 151.0px;"></div><div class="point noise" style="left: 502.0px; top: 192.0px;"></div><div class="point noise" style="left: 72.0px; top: 158.0px;"></div><div class="point noise" style="left: 228.0px; top: 302.0px;"></div><div class="point noise" style="left: 790.0px; top: 337.0px;"></div><div class="point" style="left: 732.0px; top: 196.0px; background-color: #00FF00;"></div><div class="point noise" style="left: 500.0px; top: 367.0px;"></div><div class="point noise" style="left: 619.0px; top: 417.0px;"></div><div class="point" style="left: 310.0px; top: 17.0px; background-color: #FF0000;"></div><div class="point" style="left: 746.0px; top: 86.0px; background-color: #00FF00;"></div><div class="point noise" style="left: 142.0px; top: 81.0px;"></div><div class="point" style="left: 302.0px; top: 269.0px; background-color: #0000FF;"></div><div class="point" style="left: 387.0px; top: 52.0px; background-color: #FFFF00;"></div><div class="point" style="left: 465.0px; top: 28.0px; background-color: #FFFF00;"></div><div class="point" style="left: 281.0px; top: 56.0px; background-color: #FF0000;"></div><div class="point noise" style="left: 412.0px; top: 179.0px;"></div><div class="point noise" style="left: 809.0px; top: 98.0px;"></div><div class="point noise" style="left: 519.0px; top: 66.0px;"></div><div class="point" style="left: 791.0px; top: 206.0px; background-color: #00FF00;"></div><div class="point" style="left: 428.0px; top: 101.0px; background-color: #FFFF00;"></div><div class="point noise" style="left: 781.0px; top: 249.0px;"></div><div class="point noise" style="left: 618.0px; top: 107.0px;"></div><div class="point" style="left: 353.0px; top: 328.0px; background-color: #0000FF;"></div><div class="point noise" style="left: 88.0px; top: 250.0px;"></div><div class="point" style="left: 337.0px; top: 114.0px; background-color: #FF0000;"></div><div class="point" style="left: 286.0px; top: 338.0px; background-color: #0000FF;"></div><div class="point" style="left: 718.0px; top: 131.0px; background-color: #00FF00;"></div><div class="point" style="left: 222.0px; top: 43.0px; background-color: #FF0000;"></div><div class="point" style="left: 368.0px; top: 270.0px; background-color: #0000FF;"></div><div class="point" style="left: 684.0px; top: 216.0px; background-color: #00FF00;"></div><div class="point" style="left: 426.0px; top: 41.0px; background-color: #FFFF00;"></div><div class="point noise" style="left: 664.0px; top: 306.0px;"></div>
    </div>

    <h2>聚类统计信息</h2>
    <table border="1" style="border-collapse: collapse;">
        <tr><th>簇ID</th><th>目标数量</th><th>颜色</th></tr>
<tr><td>簇 0</td><td>5</td><td style="background-color: #FF0000; width: 50px;">&nbsp;</td></tr><tr><td>簇 1</td><td>6</td><td style="background-color: #00FF00; width: 50px;">&nbsp;</td></tr><tr><td>簇 2</td><td>6</td><td style="background-color: #0000FF; width: 50px;">&nbsp;</td></tr><tr><td>簇 3</td><td>4</td><td style="background-color: #FFFF00; width: 50px;">&nbsp;</td></tr><tr><td>噪声点</td><td>24</td><td style="background-color: #888; width: 50px;">&nbsp;</td></tr>
    </table>
</body>
</html>
