#!/usr/bin/env python3
"""
测试第5版系统修复效果的脚本 V2
主要测试修复的问题：
1. YOLO时间记录问题
2. Size显示问题
3. Mask偏移功能
4. 无目标时的图像保存
5. 聚类索引越界问题
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_syntax_check():
    """测试语法检查"""
    print("=== 语法检查测试 ===")
    
    import subprocess
    
    files_to_check = ['position_5th.py', 'test_Run_5th.py']
    
    for filename in files_to_check:
        try:
            result = subprocess.run(['python', '-m', 'py_compile', filename], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✓ {filename} 语法检查通过")
            else:
                print(f"✗ {filename} 语法检查失败:")
                print(result.stderr)
        except Exception as e:
            print(f"✗ {filename} 语法检查异常: {e}")

def test_imports():
    """测试模块导入"""
    print("\n=== 模块导入测试 ===")
    
    try:
        # 测试核心函数导入
        from position_5th import (
            apply_mask_offset, 
            PerformanceLogger,
            PositionClustering,
            process_vision_v5
        )
        print("✓ 核心函数导入成功")
        
        # 测试apply_mask_offset函数
        import numpy as np
        test_mask = np.ones((100, 100), dtype=bool)
        offset_mask = apply_mask_offset(test_mask, -2, -4)
        print(f"✓ apply_mask_offset函数正常工作，输出形状: {offset_mask.shape}")
        
    except ImportError as e:
        print(f"✗ 模块导入失败: {e}")
    except Exception as e:
        print(f"✗ 函数测试失败: {e}")

def test_performance_logger():
    """测试性能记录器"""
    print("\n=== 性能记录器测试 ===")
    
    try:
        from position_5th import PerformanceLogger
        import tempfile
        
        with tempfile.TemporaryDirectory() as temp_dir:
            logger = PerformanceLogger(log_dir=temp_dir)
            
            # 测试会话记录
            logger.start_session("test_image.jpg")
            logger.record_yolo_time(0.123)
            logger.record_marigold_time(0.456)
            logger.record_postprocess_time(0.078)
            
            # 模拟目标信息
            targets_info = [
                {'index': 0, 'pixel_x': 100, 'pixel_y': 100, 'size': 0.025}
            ]
            
            result = logger.end_session(targets_info, [1, 1, 1, 2, 2])
            
            print("✓ 性能记录器工作正常")
            print(f"  YOLO时间: {result['yolo_latency']:.3f}s")
            print(f"  后处理时间: {result['postprocess_latency']:.3f}s")
            print(f"  总时间: {result['total_latency']:.3f}s")
            
    except Exception as e:
        print(f"✗ 性能记录器测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_clustering_fixes():
    """测试聚类修复"""
    print("\n=== 聚类修复测试 ===")
    
    try:
        from position_5th import PositionClustering
        import numpy as np
        
        clustering = PositionClustering()
        
        # 测试空目标列表
        empty_result = clustering.cluster_targets([])
        print("✓ 空目标列表处理正常")
        
        # 测试单个目标
        single_target = [{'pixel_x': 100, 'pixel_y': 100}]
        single_result = clustering.cluster_targets(single_target)
        print("✓ 单个目标处理正常")
        print(f"  valid_indices: {single_result.get('valid_indices', 'Missing!')}")
        
        # 测试多个目标
        multi_targets = [
            {'pixel_x': 100, 'pixel_y': 100},
            {'pixel_x': 200, 'pixel_y': 200},
            {'pixel_x': 300, 'pixel_y': 300}
        ]
        multi_result = clustering.cluster_targets(multi_targets)
        print("✓ 多个目标处理正常")
        print(f"  聚类数量: {len(multi_result['cluster_centers'])}")
        print(f"  valid_indices: {multi_result.get('valid_indices', 'Missing!')}")
        
        # 测试可视化函数（模拟）
        test_image = np.ones((480, 640, 3), dtype=np.uint8) * 255
        try:
            result_image = clustering.visualize_clusters(test_image, multi_targets, multi_result)
            print("✓ 聚类可视化正常")
        except Exception as e:
            print(f"✗ 聚类可视化失败: {e}")
            
    except Exception as e:
        print(f"✗ 聚类测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_mask_offset():
    """测试mask偏移功能"""
    print("\n=== Mask偏移功能测试 ===")
    
    try:
        from position_5th import apply_mask_offset
        import numpy as np
        
        # 创建测试mask
        original_mask = np.zeros((100, 100), dtype=bool)
        original_mask[40:60, 40:60] = True  # 中心20x20区域
        
        # 测试不同偏移
        offsets = [
            (0, 0),    # 无偏移
            (-2, -4),  # 向左上偏移
            (2, 4),    # 向右下偏移
            (-10, 0),  # 仅x偏移
            (0, -10),  # 仅y偏移
        ]
        
        for offset_x, offset_y in offsets:
            offset_mask = apply_mask_offset(original_mask, offset_x, offset_y)
            original_count = np.sum(original_mask)
            offset_count = np.sum(offset_mask)
            
            print(f"✓ 偏移({offset_x:3d}, {offset_y:3d}): 原始{original_count}像素 -> 偏移后{offset_count}像素")
            
    except Exception as e:
        print(f"✗ Mask偏移测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_size_display():
    """测试尺寸显示修复"""
    print("\n=== 尺寸显示测试 ===")
    
    # 测试尺寸转换逻辑
    test_sizes = [0.025, 0.030, 0.015, 0.040]  # 米为单位
    
    print("尺寸转换测试:")
    for size_m in test_sizes:
        size_mm = size_m * 1000
        print(f"  {size_m:.3f}m -> {size_mm:.1f}mm")
    
    print("✓ 尺寸显示逻辑正确")

def test_no_targets_handling():
    """测试无目标处理"""
    print("\n=== 无目标处理测试 ===")
    
    try:
        from position_5th import process_vision_v5
        import numpy as np
        
        # 创建模拟的无目标结果
        class MockResults:
            def __init__(self):
                self.masks = None
        
        class MockDepthFrame:
            def get_distance(self, x, y):
                return 0.5
        
        class MockIntrinsics:
            pass
        
        # 测试无目标情况
        test_image = np.ones((480, 640, 3), dtype=np.uint8) * 255
        mock_results = MockResults()
        mock_depth_frame = MockDepthFrame()
        mock_depth_intrin = MockIntrinsics()
        
        result_image, targets_info, processing_info = process_vision_v5(
            imgrgb=test_image,
            savePath=None,  # 不保存文件
            results=mock_results,
            depth_frame=mock_depth_frame,
            depth_intrin=mock_depth_intrin,
            TSIZE=0.02,
            SIZEBIAS=0.0,
            strategy_params=[1, 1, 1, 2, 2],
            imgname="test_no_targets"
        )
        
        print("✓ 无目标情况处理正常")
        print(f"  返回目标数量: {len(targets_info)}")
        print(f"  结果图像形状: {result_image.shape}")
        
    except Exception as e:
        print(f"✗ 无目标处理测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("第5版系统修复验证测试 V2")
    print("=" * 60)
    
    # 执行各项测试
    test_syntax_check()
    test_imports()
    test_performance_logger()
    test_clustering_fixes()
    test_mask_offset()
    test_size_display()
    test_no_targets_handling()
    
    print("\n" + "=" * 60)
    print("修复验证测试完成！")
    print("\n修复内容总结：")
    print("1. ✓ 修复了YOLO时间记录问题（避免重复初始化）")
    print("2. ✓ 修复了尺寸显示问题（米转毫米显示）")
    print("3. ✓ 添加了mask偏移功能（apply_mask_offset）")
    print("4. ✓ 确保无目标时也保存可视化图像")
    print("5. ✓ 修复了聚类索引越界问题（边界检查）")

if __name__ == "__main__":
    main()
