# 蘑菇采摘机器人视觉系统第5版 - 代码修改总结

## 修改内容

### 1. 日志文件管理优化
**问题**：程序每分钟创建一个新的日志文件，导致文件碎片化
**解决方案**：
- 修改`PerformanceLogger`类，使用第一张图的时间戳命名CSV文件
- 整个程序运行期间所有图片数据都保存在同一个文件中
- 每处理完一张图片立即追加写入，确保数据不丢失

### 2. 半透明mask绘制优化
**问题**：多个目标mask重叠时，半透明效果叠加导致图像过暗
**解决方案**：
- 修改`draw_masks_on_image`函数
- 创建单独的overlay层累积所有mask填充
- 一次性混合到原图，避免重复叠加透明度

### 3. 聚类算法详解
**原理**：
- 使用K-means算法基于目标像素位置(x,y)进行聚类
- 聚类数量通过肘部法则(Elbow Method)自动确定：
  - ≤2个目标：1个簇
  - 3-5个目标：2个簇  
  - 6-10个目标：3个簇
  - >10个目标：使用肘部法则分析聚类内平方和(inertia)的二阶差分
- 每个簇按包含目标数量排序（大簇优先）
- 为不同簇分配HSV色彩空间的不同颜色用于可视化

### 4. 采摘顺序规划机制
**重要说明**：
- **聚类功能主要用于可视化，不直接影响采摘顺序**
- 采摘顺序由`order_strategy`参数决定：
  - 1: 按深度排序（距离近的先采摘）
  - 2: 凸包算法（从外围到内部）
  - 3: 圆度排序（根据mask的圆度特征）
- 最终存入日志的`target_info`顺序就是规划后的采摘顺序
- 每个目标都有`pick_order`字段标识采摘序号

## 日志文件格式说明

CSV文件包含以下列：
- `timestamp`: 处理时间戳
- `imgname`: 图像文件名
- `yolo_latency`: YOLO推理时间(秒)
- `marigold_latency`: Marigold推理时间(秒，当前版本为0)
- `postprocess_latency`: 后处理时间(秒)
- `total_latency`: 总处理时间(秒)
- `target_count`: 目标数量
- `strategy_params`: 策略参数组合
- `target_info`: 目标详细信息，格式为：`[order,x,y,D,X,Y,Z,α,β,γ]`
  - order: 采摘顺序(1开始)
  - x,y: 采摘点像素坐标
  - D: 真实尺寸(米)
  - X,Y,Z: 采摘点真实三维坐标(米)
  - α,β,γ: 法线方向向量(如果开启)

## 策略参数说明

格式：`[pick_strategy, size_strategy, order_strategy, clustering, normal_calc]`

- `pick_strategy`: 1=几何中心, 2=最高点, 3=最缓点
- `size_strategy`: 1=最长线段, 2=星型线段, 3=圆形拟合  
- `order_strategy`: 1=按深度, 2=凸包算法, 3=圆度排序
- `clustering`: 1=开启聚类, 2=关闭聚类
- `normal_calc`: 1=计算法线, 2=不计算法线

## 回答用户问题

1. **日志文件**：已修改为每次程序运行只生成一个CSV文件，以第一张图时间戳命名
2. **mask绘制**：已优化半透明叠加机制，避免过度遮挡
3. **聚类算法**：使用K-means+肘部法则，簇按大小排序，大簇优先
4. **采摘顺序**：target_info中的顺序就是最终采摘顺序，聚类仅用于可视化不影响顺序规划
