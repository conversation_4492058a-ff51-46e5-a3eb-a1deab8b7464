#!/usr/bin/env python3
"""
第5版蘑菇采摘机器人视觉系统测试脚本
测试各种策略组合的正确性，验证可视化效果和数据记录功能
"""

import cv2
import numpy as np
import torch
import time
import os
from ultralytics import YOL<PERSON>
from position_5th import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>, PerformanceLogger, 
    get_picking_point_strategy, get_size_estimation_strategy, get_sequence_planning_strategy,
    process_vision_v5
)

def create_mock_depth_frame():
    """创建模拟深度帧用于测试"""
    class MockDepthFrame:
        def __init__(self, width=848, height=480):
            self.width = width
            self.height = height
            # 创建模拟深度数据，中心区域深度较小（距离较近）
            self.depth_data = np.ones((height, width)) * 0.5  # 默认0.5米
            # 在中心区域创建一些较近的点
            center_x, center_y = width // 2, height // 2
            for i in range(-50, 51):
                for j in range(-50, 51):
                    x, y = center_x + i, center_y + j
                    if 0 <= x < width and 0 <= y < height:
                        distance = np.sqrt(i*i + j*j)
                        if distance < 50:
                            self.depth_data[y, x] = 0.3 + distance * 0.002  # 0.3-0.4米范围
        
        def get_distance(self, x, y):
            if 0 <= x < self.width and 0 <= y < self.height:
                return float(self.depth_data[y, x])
            return 0.0
    
    return MockDepthFrame()

def create_mock_depth_intrin():
    """创建模拟深度相机内参"""
    class MockIntrinsics:
        def __init__(self):
            self.fx = 600.0
            self.fy = 600.0
            self.ppx = 424.0
            self.ppy = 240.0
    
    return MockIntrinsics()

def create_test_image_with_circles():
    """创建包含圆形目标的测试图像"""
    # 创建白色背景图像
    image = np.ones((480, 848, 3), dtype=np.uint8) * 255
    
    # 添加一些圆形目标（模拟蘑菇）
    circles = [
        (200, 150, 30, (100, 50, 200)),   # 紫色圆形
        (400, 200, 25, (50, 150, 100)),   # 绿色圆形
        (600, 300, 35, (200, 100, 50)),   # 橙色圆形
        (300, 350, 28, (150, 200, 250)),  # 浅蓝色圆形
    ]
    
    for x, y, radius, color in circles:
        cv2.circle(image, (x, y), radius, color, -1)
        # 添加一些纹理
        cv2.circle(image, (x-5, y-5), radius//3, (color[0]//2, color[1]//2, color[2]//2), -1)
    
    return image

def create_mock_yolo_results(image):
    """创建模拟YOLO检测结果"""
    class MockResults:
        def __init__(self):
            # 创建4个圆形mask
            self.masks = MockMasks()
            self.boxes = MockBoxes()
    
    class MockMasks:
        def __init__(self):
            # 创建4个圆形mask
            masks = []
            circles = [(200, 150, 30), (400, 200, 25), (600, 300, 35), (300, 350, 28)]
            
            for x, y, radius in circles:
                mask = np.zeros((480, 848), dtype=bool)
                # 创建圆形mask
                Y, X = np.ogrid[:480, :848]
                dist_from_center = np.sqrt((X - x)**2 + (Y - y)**2)
                mask[dist_from_center <= radius] = True
                masks.append(mask)
            
            self.data = torch.tensor(np.array(masks), dtype=torch.float32)
    
    class MockBoxes:
        def __init__(self):
            self.conf = torch.tensor([0.9, 0.8, 0.85, 0.75])
    
    return MockResults()

def test_strategy_combinations():
    """测试不同策略组合"""
    print("=== 测试不同策略组合 ===")
    
    # 创建测试数据
    test_image = create_test_image_with_circles()
    mock_results = create_mock_yolo_results(test_image)
    mock_depth_frame = create_mock_depth_frame()
    mock_depth_intrin = create_mock_depth_intrin()
    
    # 测试不同的策略组合
    test_strategies = [
        [1, 1, 1, 2, 2],  # 几何中心 + 最长线段 + 按深度 + 不聚类 + 不计算法线
        [2, 2, 2, 1, 2],  # 最高点 + 星型线段 + 凸包 + 聚类 + 不计算法线
        [3, 3, 3, 2, 1],  # 最缓点 + 圆形拟合 + 圆度 + 不聚类 + 计算法线
        [1, 2, 3, 1, 2],  # 混合策略组合
    ]
    
    # 初始化性能记录器
    logger = PerformanceLogger(log_dir="test_logs")
    
    for i, strategy_params in enumerate(test_strategies):
        print(f"\n--- 测试策略组合 {i+1}: {strategy_params} ---")
        
        try:
            # 使用第5版视觉系统处理
            result_image, targets_info, processing_info = process_vision_v5(
                imgrgb=test_image,
                savePath=f"test_result_{i+1}.jpg",
                results=mock_results,
                depth_frame=mock_depth_frame,
                depth_intrin=mock_depth_intrin,
                TSIZE=0.02,  # 20mm
                SIZEBIAS=0.0,
                strategy_params=strategy_params,
                imgname=f"test_image_{i+1}",
                save_normal=False,
                logger=logger
            )
            
            print(f"处理成功！检测到 {len(targets_info)} 个目标")
            
            # 显示结果信息
            for j, target in enumerate(targets_info):
                print(f"  目标 {j+1}: 位置({target['pixel_x']:.1f}, {target['pixel_y']:.1f}), "
                      f"尺寸{target['size']:.3f}m, 类型{target.get('pick_info', {}).get('type', 'unknown')}")
            
        except Exception as e:
            print(f"策略组合 {i+1} 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 打印性能统计
    logger.print_statistics()

def test_individual_strategies():
    """测试各个策略模块"""
    print("\n=== 测试各个策略模块 ===")
    
    # 创建测试mask
    mask = np.zeros((100, 100), dtype=bool)
    mask[30:70, 30:70] = True  # 40x40的方形区域
    
    mock_depth_frame = create_mock_depth_frame()
    mock_depth_intrin = create_mock_depth_intrin()
    
    # 测试采摘点位策略
    print("\n--- 测试采摘点位策略 ---")
    for strategy_id in [1, 2, 3]:
        strategy = get_picking_point_strategy(strategy_id)
        try:
            pixel_x, pixel_y, world_point, pick_info = strategy.get_picking_point(
                mask, mock_depth_frame, mock_depth_intrin
            )
            print(f"策略 {strategy_id}: 采摘点({pixel_x}, {pixel_y}), 类型{pick_info.get('type', 'unknown') if pick_info else 'None'}")
        except Exception as e:
            print(f"策略 {strategy_id} 失败: {e}")
    
    # 测试尺寸判断策略
    print("\n--- 测试尺寸判断策略 ---")
    for strategy_id in [1, 2, 3]:
        strategy = get_size_estimation_strategy(strategy_id)
        try:
            size, measurement_points, size_info = strategy.estimate_size(
                mask, mock_depth_frame, mock_depth_intrin, 0.0
            )
            print(f"策略 {strategy_id}: 尺寸{size:.3f}m, 类型{size_info.get('type', 'unknown') if size_info else 'None'}")
        except Exception as e:
            print(f"策略 {strategy_id} 失败: {e}")
    
    # 测试顺序规划策略
    print("\n--- 测试顺序规划策略 ---")
    targets_info = [
        {'pixel_x': 100, 'pixel_y': 100, 'world_point': [0.1, 0.1, 0.3], 'mask': mask},
        {'pixel_x': 200, 'pixel_y': 150, 'world_point': [0.2, 0.15, 0.25], 'mask': mask},
        {'pixel_x': 150, 'pixel_y': 200, 'world_point': [0.15, 0.2, 0.35], 'mask': mask},
    ]
    
    for strategy_id in [1, 2, 3]:
        strategy = get_sequence_planning_strategy(strategy_id)
        try:
            sequence = strategy.plan_sequence(targets_info)
            print(f"策略 {strategy_id}: 采摘顺序 {sequence}")
        except Exception as e:
            print(f"策略 {strategy_id} 失败: {e}")

def main():
    """主测试函数"""
    print("第5版蘑菇采摘机器人视觉系统测试")
    print("=" * 50)
    
    # 创建测试目录
    os.makedirs("test_logs", exist_ok=True)
    
    # 测试各个策略模块
    test_individual_strategies()
    
    # 测试策略组合
    test_strategy_combinations()
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
