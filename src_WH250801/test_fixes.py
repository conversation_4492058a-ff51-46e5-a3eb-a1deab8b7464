#!/usr/bin/env python3
"""
测试第5版系统修复效果的脚本
主要测试：
1. 聚类功能是否正常工作
2. 可视化颜色是否正确
3. 文字显示是否正常
"""

import numpy as np
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_clustering_fix():
    """测试聚类功能修复"""
    print("=== 测试聚类功能修复 ===")
    
    try:
        from position_5th import PositionClustering
        
        # 创建测试目标信息
        targets_info = [
            {'pixel_x': 100, 'pixel_y': 100, 'mask': np.ones((50, 50), dtype=bool)},
            {'pixel_x': 110, 'pixel_y': 105, 'mask': np.ones((50, 50), dtype=bool)},
            {'pixel_x': 300, 'pixel_y': 200, 'mask': np.ones((50, 50), dtype=bool)},
            {'pixel_x': 310, 'pixel_y': 205, 'mask': np.ones((50, 50), dtype=bool)},
            {'pixel_x': 500, 'pixel_y': 300, 'mask': np.ones((50, 50), dtype=bool)},
        ]
        
        clustering = PositionClustering()
        cluster_result = clustering.cluster_targets(targets_info)
        
        print("✓ 聚类功能执行成功")
        print(f"  聚类标签类型: {type(cluster_result['cluster_labels'])}")
        print(f"  聚类标签: {cluster_result['cluster_labels']}")
        print(f"  聚类中心数: {len(cluster_result['cluster_centers'])}")
        
        # 测试可视化函数
        test_image = np.ones((480, 640, 3), dtype=np.uint8) * 255  # 白色背景
        
        try:
            result_image = clustering.visualize_clusters(test_image, targets_info, cluster_result)
            print("✓ 聚类可视化执行成功")
            print(f"  输出图像形状: {result_image.shape}")
        except Exception as e:
            print(f"✗ 聚类可视化失败: {e}")
            import traceback
            traceback.print_exc()
            
    except ImportError as e:
        print(f"✗ 导入聚类模块失败: {e}")
    except Exception as e:
        print(f"✗ 聚类测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_visualization_fix():
    """测试可视化修复"""
    print("\n=== 测试可视化修复 ===")
    
    try:
        from position_5th import create_result_visualization
        
        # 创建测试图像和目标信息
        test_image = np.ones((480, 640, 3), dtype=np.uint8) * 255  # RGB白色背景
        
        targets_info = [
            {
                'pixel_x': 200, 'pixel_y': 150, 'size': 0.025,
                'mask': np.zeros((480, 640), dtype=bool),
                'pick_info': {'type': 'geometric_center'},
                'size_info': {'type': 'longest_diameter'},
                'measurement_points': [(190, 150), (210, 150)]
            },
            {
                'pixel_x': 400, 'pixel_y': 300, 'size': 0.030,
                'mask': np.zeros((480, 640), dtype=bool),
                'pick_info': {'type': 'highest_point'},
                'size_info': {'type': 'star_shaped'},
                'measurement_points': [(390, 300), (410, 300)]
            }
        ]
        
        # 为mask添加一些内容
        for i, target in enumerate(targets_info):
            x, y = int(target['pixel_x']), int(target['pixel_y'])
            # 创建圆形mask
            Y, X = np.ogrid[:480, :640]
            dist_from_center = np.sqrt((X - x)**2 + (Y - y)**2)
            target['mask'][dist_from_center <= 20] = True
        
        # 测试不带聚类的可视化
        try:
            result_image = create_result_visualization(test_image, targets_info)
            print("✓ 基础可视化执行成功")
            print(f"  输出图像形状: {result_image.shape}")
            print(f"  输出图像数据类型: {result_image.dtype}")
        except Exception as e:
            print(f"✗ 基础可视化失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 测试带聚类的可视化
        try:
            from position_5th import PositionClustering
            clustering = PositionClustering()
            cluster_result = clustering.cluster_targets(targets_info)
            
            result_image = create_result_visualization(test_image, targets_info, cluster_result)
            print("✓ 聚类可视化执行成功")
            print(f"  输出图像形状: {result_image.shape}")
        except Exception as e:
            print(f"✗ 聚类可视化失败: {e}")
            import traceback
            traceback.print_exc()
            
    except ImportError as e:
        print(f"✗ 导入可视化模块失败: {e}")
    except Exception as e:
        print(f"✗ 可视化测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_strategy_controller():
    """测试策略控制器"""
    print("\n=== 测试策略控制器 ===")
    
    try:
        from position_5th import StrategyController
        
        # 测试不同策略组合
        test_strategies = [
            [1, 1, 1, 2, 2],  # 基础组合
            [2, 2, 2, 1, 2],  # 带聚类组合
            [3, 3, 3, 1, 1],  # 高级组合
        ]
        
        for i, strategy_params in enumerate(test_strategies):
            try:
                controller = StrategyController(strategy_params)
                description = controller.get_strategy_description()
                print(f"✓ 策略组合 {i+1} ({strategy_params}): {description['full_description'][:50]}...")
            except Exception as e:
                print(f"✗ 策略组合 {i+1} 失败: {e}")
                
    except ImportError as e:
        print(f"✗ 导入策略控制器失败: {e}")
    except Exception as e:
        print(f"✗ 策略控制器测试失败: {e}")

def test_array_handling():
    """测试数组处理修复"""
    print("\n=== 测试数组处理修复 ===")
    
    # 测试空数组情况
    empty_result = {
        'cluster_labels': [],
        'cluster_colors': [],
        'cluster_centers': [],
        'cluster_sizes': [],
        'valid_indices': []
    }
    
    # 测试numpy数组情况
    numpy_result = {
        'cluster_labels': np.array([0, 1, 0, 1, 2]),
        'cluster_colors': [(255, 0, 0), (0, 255, 0), (0, 0, 255)],
        'cluster_centers': [[100, 100], [300, 200], [500, 300]],
        'cluster_sizes': [2, 2, 1],
        'valid_indices': [0, 1, 2, 3, 4]
    }
    
    try:
        from position_5th import PositionClustering
        clustering = PositionClustering()
        test_image = np.ones((480, 640, 3), dtype=np.uint8) * 255
        targets_info = [{'pixel_x': 100, 'pixel_y': 100}] * 5
        
        # 测试空结果
        try:
            result = clustering.visualize_clusters(test_image, targets_info, empty_result)
            print("✓ 空数组处理成功")
        except Exception as e:
            print(f"✗ 空数组处理失败: {e}")
        
        # 测试numpy数组结果
        try:
            result = clustering.visualize_clusters(test_image, targets_info, numpy_result)
            print("✓ numpy数组处理成功")
        except Exception as e:
            print(f"✗ numpy数组处理失败: {e}")
            
    except Exception as e:
        print(f"✗ 数组处理测试失败: {e}")

def main():
    """主测试函数"""
    print("第5版系统修复效果测试")
    print("=" * 50)
    
    # 测试各项修复
    test_clustering_fix()
    test_visualization_fix()
    test_strategy_controller()
    test_array_handling()
    
    print("\n" + "=" * 50)
    print("修复测试完成！")
    print("\n修复内容总结：")
    print("1. ✓ 修复了聚类功能中的数组判断错误")
    print("2. ✓ 改进了可视化函数的色彩空间处理")
    print("3. ✓ 添加了mask轮廓绘制功能")
    print("4. ✓ 修复了中文字符显示问题（改用英文缩写）")
    print("5. ✓ 优化了图像保存的色彩空间转换")

if __name__ == "__main__":
    main()
