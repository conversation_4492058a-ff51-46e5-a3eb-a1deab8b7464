from time import sleep
import sys
sys.path.append("/usr/local/lib/")
import numpy as np
from PIL import Image
import datetime
import pyrealsense2 as rs
from pyrealsense2 import distortion
import serial
import cv2
from coreDetect import executeDetection
from coreDetect import initModel
from positionTransfer import positionTrans
#from positionTransferRow import positionTrans
from crc import crc16Add
from decimalM import transferToHex
resolution_x = 1280
resolution_y = 720

targetSize = 0  # haft of the target size
score_thres = 0.6   # filter out the score less than the threshold

# execute the object detection
#trained_model = 'mushroomsFinal_FaceBoxes.pth'
#use_cpu = False

shootCommand = '01 05 00 01 00 17 DC 04'      #01 05 00 01 00 17 DC 04
shootFailCommand = '01 05 80 01 00 01 74 0A'
getDataCommand = '01 16 00 01 00 01 D8 09'    #01 16 00 01 00 01 D8 09
getDataFailCommand = '01 16 80 01 00 01 F1 C9'

def judgeShootCommand(command):
    content = command[:-6]
    checkCode = command[-5:].lower()

    if content == shootCommand:
        crcRes = crc16Add(content)
        if crcRes == checkCode:
            return 1
            #print("01 05 00 01 FF E8 DD B4")        # correct message
        else:
            return 2
            #print("01 05 80 01 00 01 74 0A")        # wrong message
    else:
        return -1

def judgeGetDataCommand(command):
    content = command[:-6]
    checkCode = command[-5:].lower()

    if content == getDataCommand:
        crcRes = crc16Add(content)
        if crcRes == checkCode:
            return 1
        else:
            return 2
            #print("01 16 80 01 00 01 F1 C9")        # wrong message
    else:
        return -1


def recv(serial):
    while True:
        data = serial.read_all()
        if data == b'':
            continue
        else:
            break
        sleep(0.5)
    return data

if __name__ == '__main__':
    #open the serial console
    #serial = serial.Serial('COM6', 115200, timeout=0.5)       #/dev/ttyUSB0
    # the timeout parameter is important
    serial = serial.Serial(port='/dev/ttyTHS2', baudrate=115200, parity=serial.PARITY_ODD, stopbits=serial.STOPBITS_ONE,
                           bytesize=serial.EIGHTBITS, timeout=0.5)
    #serial = serial.Serial('/dev/ttyTHS2', 115200, timeout=0.5)       #/dev/ttyUSB0
    if serial.isOpen():
        print("open success")
    else :
        print("open failed")

    #config the rgb-d camera
    pipeline = rs.pipeline()
    # Create a config and configure the pipeline to stream
    #  different resolutions of color and depth streams
    config = rs.config()
    config.enable_stream(rs.stream.depth, resolution_x, resolution_y, rs.format.z16, 30)
    config.enable_stream(rs.stream.color, resolution_x, resolution_y, rs.format.bgr8, 30)

    # Start streamingSSS
    profile = pipeline.start(config)
    colorizer = rs.colorizer()
   #sleep(1)
    #intr = profile.get_stream(rs.stream.depth).as_video_stream_profile().get_intrinsics()
    #print(intr)

    # Getting the depth sensor's depth scale (see rs-align example for explanation)
    depth_sensor = profile.get_device().first_depth_sensor()
    depth_scale = depth_sensor.get_depth_scale()
    #print("Depth Scale is: ", depth_scale)

    #net = load_detection_model(trained_model, use_cpu)
    detector = initModel()
    # We will be removing the background of objects more than
    #  clipping_distance_in_meters meters away
    # clipping_distance_in_meters = 1  # 1 meter
    # clipping_distance = clipping_distance_in_meters / depth_scale

    # Create an align object
    # rs.align allows us to perform alignment of depth frames to others frames
    # The "align_to" is the stream type to which we plan to align depth frames.
    align_to = rs.stream.color
    align = rs.align(align_to)

    while True:
        data =recv(serial)
        judgeShootRes = judgeShootCommand(data)
        judgeGetDataRes = judgeGetDataCommand(data)
        if judgeShootRes == 1:
            print("receive instruction: ", data)
            # Streaming loop
            try:
                while True:
                    # Get frameset of color and depth
                    frames = pipeline.wait_for_frames()
                    # frames.get_depth_frame() is a 640x360 depth image
                    # Align the depth frame to color frame
                    aligned_frames = align.process(frames)
                    # Get aligned frames
                    color_frame = aligned_frames.get_color_frame()
                    aligned_depth_frame = aligned_frames.get_depth_frame()  # aligned_depth_frame is a 640x480 depth image
                    # Validate that both frames are valid
                    if not aligned_depth_frame or not color_frame:
                        continue
                    #depth_image = np.asanyarray(aligned_depth_frame.get_data())
                    color_image = np.asanyarray(color_frame.get_data())
                    aligned_depth_color_frame = colorizer.colorize(aligned_depth_frame)
                    depth_image = np.asanyarray(aligned_depth_color_frame.get_data())
                    break
            finally:
                print("get frames")
            imageName = '{0:%Y%m%d%H%M%S%f.jpg}'.format(datetime.datetime.now())
            imagePath = '../temp/' + imageName
            depth_imageName = imageName[:-4] + '_depth.jpg'
            depth_imagePath = '../temp/' + depth_imageName
           #im = Image.fromarray(color_image)
            #im.save(imagePath)
            cv2.imwrite(imagePath, color_image)
            cv2.imwrite(depth_imagePath, depth_image)
            dets = executeDetection(imagePath, detector, score_thres)

            dets_return = positionTrans(dets, aligned_depth_frame, score_thres, targetSize, resolution_x, resolution_y)
            num_object = str(dets_return.shape[0]) + '\n'
            # send num of objects
            #serial.write(num_object.encode())
            print(num_object)
            message = "01 03" + ' ' + transferToHex(dets_return.shape[0]*6)         #todo check out
            for k in range(dets_return.shape[0]):
                # send each object coordinate
                infor = str(dets_return[k, 0]) + ' ' + str(dets_return[k, 1]) + ' ' + str(dets_return[k, 2]) + ' ' +str(dets_return[k, 3])  + ' ' +str(dets_return[k, 4]) + ' ' +str(dets_return[k, 5])  + ' ' +str(dets_return[k, 6])  + ' ' +str(dets_return[k, 7])  +  '\n'
                print(infor)
                message = message + ' ' + transferToHex(int(dets_return[k, 5]*10000)) + ' ' + transferToHex(int(dets_return[k, 6]*10000)) + ' ' + transferToHex(int(dets_return[k, 7]*10000))
                #serial.write(infor.encode())
            crcCode = crc16Add(message)
            message = message + ' ' + crcCode
        elif judgeShootRes == 2:
            serial.write(shootFailCommand.encode())

        if judgeGetDataRes == 1:
            if message != None:
                serial.write(message.encode())
        elif judgeGetDataRes == 2:
            serial.write(getDataFailCommand.encode())


        if data == b'quit':
            print("receive termination instruction:", data)
            pipeline.stop()
            break

        if judgeGetDataRes == -1 and judgeShootRes == -1:
            print("receive other instruction:", data)

