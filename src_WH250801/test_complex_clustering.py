#!/usr/bin/env python3
"""
测试复杂聚类情况，验证修复效果
"""

def test_complex_clustering_scenario():
    """测试复杂聚类场景，其中valid_indices不是连续的"""
    print("=== 测试复杂聚类场景 ===")
    
    # 模拟一个更复杂的情况：
    # - 有10个目标，但只有部分参与聚类（比如有些目标太小被过滤掉）
    # - valid_indices不是连续的
    targets_info = []
    for i in range(10):
        targets_info.append({
            'pixel_x': 100 + i * 50, 
            'pixel_y': 100 + (i % 3) * 100, 
            'target_id': i
        })
    
    # 假设只有索引为 [1, 3, 4, 6, 8, 9] 的目标参与聚类
    # 这些目标被分为3个簇
    cluster_result = {
        'cluster_labels': [0, 0, 1, 2, 2, 1],  # 6个参与聚类的目标的簇标签
        'cluster_colors': [(255, 0, 0), (0, 255, 0), (0, 0, 255)],  # 红、绿、蓝
        'valid_indices': [1, 3, 4, 6, 8, 9],  # 参与聚类的目标索引
        'algorithm': 'K-means'
    }
    
    print(f"总目标数量: {len(targets_info)}")
    print(f"参与聚类的目标索引: {cluster_result['valid_indices']}")
    print(f"聚类标签: {cluster_result['cluster_labels']}")
    print(f"颜色数量: {len(cluster_result['cluster_colors'])}")
    
    # 测试修复前的错误逻辑
    print("\n--- 修复前的错误逻辑 ---")
    cluster_colors_old = {}
    cluster_labels = cluster_result['cluster_labels']
    colors = cluster_result['cluster_colors']
    valid_indices = cluster_result['valid_indices']
    
    # 错误的映射逻辑（原始代码）
    for i, (target_idx, cluster_label) in enumerate(zip(valid_indices, cluster_labels)):
        if target_idx < len(targets_info):
            cluster_colors_old[target_idx] = colors[cluster_label % len(colors)]
            print(f"  目标{target_idx} -> 簇{cluster_label} -> 颜色{colors[cluster_label % len(colors)]}")
    
    # 测试修复后的正确逻辑
    print("\n--- 修复后的正确逻辑 ---")
    cluster_colors_new = {}
    
    # 修复后的映射逻辑
    for i, cluster_label in enumerate(cluster_labels):
        if i < len(valid_indices):
            target_idx = valid_indices[i]  # 获取目标在 targets_info 中的真实索引
            if target_idx < len(targets_info) and cluster_label >= 0:  # 排除噪声点(-1)
                if cluster_label < len(colors):
                    cluster_colors_new[target_idx] = colors[cluster_label]
                    print(f"  目标{target_idx} -> 簇{cluster_label} -> 颜色{colors[cluster_label]}")
                else:
                    # 如果簇标签超出颜色数组范围，使用模运算
                    cluster_colors_new[target_idx] = colors[cluster_label % len(colors)]
                    print(f"  目标{target_idx} -> 簇{cluster_label}(mod) -> 颜色{colors[cluster_label % len(colors)]}")
    
    # 验证结果
    print("\n--- 结果验证 ---")
    print("修复前后的颜色映射是否相同:", cluster_colors_old == cluster_colors_new)
    
    if cluster_colors_old != cluster_colors_new:
        print("发现差异!")
        print(f"修复前: {cluster_colors_old}")
        print(f"修复后: {cluster_colors_new}")
    
    # 检查每个簇的颜色一致性
    print("\n簇颜色一致性检查:")
    for cluster_id in range(len(colors)):
        targets_in_cluster = []
        for i, label in enumerate(cluster_labels):
            if label == cluster_id and i < len(valid_indices):
                target_idx = valid_indices[i]
                targets_in_cluster.append(target_idx)
        
        if targets_in_cluster:
            expected_color = colors[cluster_id]
            print(f"  簇{cluster_id} (期望颜色{expected_color}):")
            all_same_color = True
            for target_idx in targets_in_cluster:
                actual_color = cluster_colors_new.get(target_idx, None)
                print(f"    目标{target_idx}: {actual_color}")
                if actual_color != expected_color:
                    all_same_color = False
            
            if all_same_color:
                print(f"    ✓ 簇{cluster_id}颜色一致")
            else:
                print(f"    ✗ 簇{cluster_id}颜色不一致")
    
    # 检查空间分布（模拟）
    print("\n空间分布检查:")
    for cluster_id in range(len(colors)):
        targets_in_cluster = []
        for i, label in enumerate(cluster_labels):
            if label == cluster_id and i < len(valid_indices):
                target_idx = valid_indices[i]
                if target_idx < len(targets_info):
                    target = targets_info[target_idx]
                    targets_in_cluster.append((target['pixel_x'], target['pixel_y']))
        
        if targets_in_cluster:
            print(f"  簇{cluster_id}的目标位置: {targets_in_cluster}")
            # 简单检查：同一簇的目标是否在相似的位置
            if len(targets_in_cluster) > 1:
                x_coords = [pos[0] for pos in targets_in_cluster]
                y_coords = [pos[1] for pos in targets_in_cluster]
                x_range = max(x_coords) - min(x_coords)
                y_range = max(y_coords) - min(y_coords)
                print(f"    X坐标范围: {x_range}, Y坐标范围: {y_range}")
                
                if x_range < 100 and y_range < 100:  # 假设100像素内算集中
                    print(f"    ✓ 簇{cluster_id}空间集中")
                else:
                    print(f"    ✗ 簇{cluster_id}空间分散")

def test_problematic_scenario():
    """测试会导致原始逻辑出错的场景"""
    print("\n=== 测试问题场景 ===")
    print("这个场景会暴露原始逻辑的问题")
    
    # 创建一个会导致原始逻辑出错的场景
    targets_info = [
        {'pixel_x': 100, 'pixel_y': 100, 'target_id': 0},  # 不参与聚类
        {'pixel_x': 200, 'pixel_y': 100, 'target_id': 1},  # 簇0
        {'pixel_x': 300, 'pixel_y': 100, 'target_id': 2},  # 不参与聚类
        {'pixel_x': 400, 'pixel_y': 100, 'target_id': 3},  # 簇1
        {'pixel_x': 500, 'pixel_y': 100, 'target_id': 4},  # 簇0
    ]
    
    # 只有目标1, 3, 4参与聚类，分为2个簇
    cluster_result = {
        'cluster_labels': [0, 1, 0],  # 目标1->簇0, 目标3->簇1, 目标4->簇0
        'cluster_colors': [(255, 0, 0), (0, 255, 0)],  # 红、绿
        'valid_indices': [1, 3, 4],  # 参与聚类的目标索引
        'algorithm': 'K-means'
    }
    
    print(f"目标位置:")
    for i, target in enumerate(targets_info):
        participate = "参与" if i in cluster_result['valid_indices'] else "不参与"
        print(f"  目标{i}: ({target['pixel_x']}, {target['pixel_y']}) - {participate}聚类")
    
    print(f"\n聚类结果:")
    print(f"  参与聚类的目标: {cluster_result['valid_indices']}")
    print(f"  对应的簇标签: {cluster_result['cluster_labels']}")
    
    # 原始错误逻辑的问题演示
    print(f"\n原始逻辑的问题:")
    print(f"  原始逻辑会将 valid_indices[i] 和 cluster_labels[i] 错误配对")
    print(f"  例如: zip([1,3,4], [0,1,0]) = [(1,0), (3,1), (4,0)]")
    print(f"  这意味着目标1->簇0, 目标3->簇1, 目标4->簇0")
    print(f"  但实际上应该是: cluster_labels[0]对应valid_indices[0]的目标")
    
    # 正确的逻辑
    print(f"\n修复后的正确逻辑:")
    cluster_colors = {}
    cluster_labels = cluster_result['cluster_labels']
    colors = cluster_result['cluster_colors']
    valid_indices = cluster_result['valid_indices']
    
    for i, cluster_label in enumerate(cluster_labels):
        if i < len(valid_indices):
            target_idx = valid_indices[i]
            if target_idx < len(targets_info) and cluster_label >= 0:
                cluster_colors[target_idx] = colors[cluster_label]
                target_pos = (targets_info[target_idx]['pixel_x'], targets_info[target_idx]['pixel_y'])
                print(f"  目标{target_idx} (位置{target_pos}) -> 簇{cluster_label} -> 颜色{colors[cluster_label]}")
    
    print(f"\n最终颜色映射: {cluster_colors}")

if __name__ == "__main__":
    test_complex_clustering_scenario()
    test_problematic_scenario()
