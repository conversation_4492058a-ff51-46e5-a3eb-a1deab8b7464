#!/usr/bin/env python3
"""
测试聚类逻辑的简单脚本，不依赖OpenCV
"""

import numpy as np
import random

def create_simple_test_targets(n_targets=20):
    """创建简单的测试目标数据"""
    targets_info = []
    
    # 创建3个聚集区域
    cluster_centers = [
        (100, 100),  # 区域1
        (300, 100),  # 区域2  
        (200, 300),  # 区域3
    ]
    
    cluster_sizes = [6, 8, 6]  # 每个区域的目标数量
    
    target_id = 0
    for cluster_id, (center_x, center_y) in enumerate(cluster_centers):
        n_in_cluster = cluster_sizes[cluster_id]
        
        for i in range(n_in_cluster):
            # 在聚类中心周围随机分布
            offset_x = random.gauss(0, 20)  # 标准差20像素
            offset_y = random.gauss(0, 20)
            
            pixel_x = center_x + offset_x
            pixel_y = center_y + offset_y
            
            target = {
                'pixel_x': pixel_x,
                'pixel_y': pixel_y,
                'size': 0.03 + random.random() * 0.02,  # 0.03-0.05m
                'target_id': target_id,
                'expected_cluster': cluster_id  # 期望的簇标签
            }
            targets_info.append(target)
            target_id += 1
    
    return targets_info

def test_clustering_color_mapping():
    """测试聚类颜色映射逻辑"""
    print("=== 测试聚类颜色映射逻辑 ===")
    
    # 导入聚类策略
    try:
        from position_5th import KMeansClusteringStrategy
    except ImportError as e:
        print(f"导入失败: {e}")
        return
    
    # 创建测试数据
    targets_info = create_simple_test_targets(20)
    print(f"创建了{len(targets_info)}个测试目标")
    
    # 打印目标位置
    print("\n目标位置分布:")
    for i, target in enumerate(targets_info):
        print(f"目标{i}: ({target['pixel_x']:.1f}, {target['pixel_y']:.1f}) - 期望簇{target['expected_cluster']}")
    
    # 测试K-means聚类
    print("\n--- 测试K-means聚类 ---")
    kmeans_strategy = KMeansClusteringStrategy(n_clusters=3)  # 手动指定3个簇
    kmeans_result = kmeans_strategy.cluster_targets(targets_info)
    
    if kmeans_result:
        print(f"K-means聚类结果:")
        print(f"  算法: {kmeans_result.get('algorithm', 'Unknown')}")
        print(f"  簇数量: {len(kmeans_result.get('sorted_clusters', []))}")
        print(f"  簇大小: {kmeans_result.get('cluster_sizes', [])}")
        
        cluster_labels = kmeans_result.get('cluster_labels', [])
        cluster_colors = kmeans_result.get('cluster_colors', [])
        valid_indices = kmeans_result.get('valid_indices', [])
        
        print(f"  聚类标签: {cluster_labels}")
        print(f"  有效索引: {valid_indices}")
        print(f"  颜色数量: {len(cluster_colors)}")
        
        # 测试颜色映射逻辑（模拟draw_masks_on_image中的逻辑）
        print("\n颜色映射测试:")
        color_mapping = {}
        
        # 修复后的映射逻辑
        for i, cluster_label in enumerate(cluster_labels):
            if i < len(valid_indices):
                target_idx = valid_indices[i]  # 获取目标在 targets_info 中的真实索引
                if target_idx < len(targets_info) and cluster_label >= 0:  # 排除噪声点(-1)
                    if cluster_label < len(cluster_colors):
                        color_mapping[target_idx] = cluster_colors[cluster_label]
                        print(f"  目标{target_idx} -> 簇{cluster_label} -> 颜色{cluster_colors[cluster_label]}")
                    else:
                        # 如果簇标签超出颜色数组范围，使用模运算
                        color_mapping[target_idx] = cluster_colors[cluster_label % len(cluster_colors)]
                        print(f"  目标{target_idx} -> 簇{cluster_label}(mod) -> 颜色{cluster_colors[cluster_label % len(cluster_colors)]}")
        
        # 验证每个簇的目标是否在空间上集中
        print("\n簇空间集中性验证:")
        for cluster_id in range(len(cluster_colors)):
            cluster_targets = []
            for i, label in enumerate(cluster_labels):
                if label == cluster_id and i < len(valid_indices):
                    target_idx = valid_indices[i]
                    if target_idx < len(targets_info):
                        target = targets_info[target_idx]
                        cluster_targets.append((target['pixel_x'], target['pixel_y']))
            
            if cluster_targets:
                # 计算簇内目标的平均位置和标准差
                positions = np.array(cluster_targets)
                mean_pos = np.mean(positions, axis=0)
                std_pos = np.std(positions, axis=0)
                print(f"  簇{cluster_id}: {len(cluster_targets)}个目标")
                print(f"    中心位置: ({mean_pos[0]:.1f}, {mean_pos[1]:.1f})")
                print(f"    位置标准差: ({std_pos[0]:.1f}, {std_pos[1]:.1f})")
                
                # 检查是否空间集中（标准差应该相对较小）
                if std_pos[0] < 50 and std_pos[1] < 50:
                    print(f"    ✓ 簇{cluster_id}空间集中")
                else:
                    print(f"    ✗ 簇{cluster_id}空间分散")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    # 设置随机种子以获得可重复的结果
    random.seed(42)
    np.random.seed(42)
    
    test_clustering_color_mapping()
