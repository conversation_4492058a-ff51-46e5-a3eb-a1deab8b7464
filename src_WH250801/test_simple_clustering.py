#!/usr/bin/env python3
"""
简单的聚类逻辑测试，不依赖外部库
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_color_mapping_logic():
    """测试颜色映射逻辑"""
    print("=== 测试聚类颜色映射逻辑 ===")
    
    # 模拟聚类结果数据
    targets_info = [
        {'pixel_x': 100, 'pixel_y': 100, 'target_id': 0},
        {'pixel_x': 110, 'pixel_y': 105, 'target_id': 1},
        {'pixel_x': 300, 'pixel_y': 100, 'target_id': 2},
        {'pixel_x': 310, 'pixel_y': 95, 'target_id': 3},
        {'pixel_x': 200, 'pixel_y': 300, 'target_id': 4},
        {'pixel_x': 205, 'pixel_y': 310, 'target_id': 5},
    ]
    
    # 模拟聚类结果
    cluster_result = {
        'cluster_labels': [0, 0, 1, 1, 2, 2],  # 每个目标的簇标签
        'cluster_colors': [(255, 0, 0), (0, 255, 0), (0, 0, 255)],  # 红、绿、蓝
        'valid_indices': [0, 1, 2, 3, 4, 5],  # 所有目标都参与聚类
        'algorithm': 'K-means'
    }
    
    print(f"目标数量: {len(targets_info)}")
    print(f"聚类标签: {cluster_result['cluster_labels']}")
    print(f"有效索引: {cluster_result['valid_indices']}")
    print(f"颜色数量: {len(cluster_result['cluster_colors'])}")
    
    # 测试修复前的错误逻辑（会导致颜色分配错误）
    print("\n--- 修复前的错误逻辑 ---")
    cluster_colors_old = {}
    cluster_labels = cluster_result['cluster_labels']
    colors = cluster_result['cluster_colors']
    valid_indices = cluster_result['valid_indices']
    
    # 错误的映射逻辑（原始代码）
    for i, (target_idx, cluster_label) in enumerate(zip(valid_indices, cluster_labels)):
        if target_idx < len(targets_info):
            cluster_colors_old[target_idx] = colors[cluster_label % len(colors)]
            print(f"  目标{target_idx} -> 簇{cluster_label} -> 颜色{colors[cluster_label % len(colors)]}")
    
    # 测试修复后的正确逻辑
    print("\n--- 修复后的正确逻辑 ---")
    cluster_colors_new = {}
    
    # 修复后的映射逻辑
    for i, cluster_label in enumerate(cluster_labels):
        if i < len(valid_indices):
            target_idx = valid_indices[i]  # 获取目标在 targets_info 中的真实索引
            if target_idx < len(targets_info) and cluster_label >= 0:  # 排除噪声点(-1)
                if cluster_label < len(colors):
                    cluster_colors_new[target_idx] = colors[cluster_label]
                    print(f"  目标{target_idx} -> 簇{cluster_label} -> 颜色{colors[cluster_label]}")
                else:
                    # 如果簇标签超出颜色数组范围，使用模运算
                    cluster_colors_new[target_idx] = colors[cluster_label % len(colors)]
                    print(f"  目标{target_idx} -> 簇{cluster_label}(mod) -> 颜色{colors[cluster_label % len(colors)]}")
    
    # 验证结果
    print("\n--- 结果验证 ---")
    print("修复前后的颜色映射是否相同:", cluster_colors_old == cluster_colors_new)
    
    # 检查每个簇的颜色一致性
    print("\n簇颜色一致性检查:")
    for cluster_id in range(len(colors)):
        targets_in_cluster = []
        for i, label in enumerate(cluster_labels):
            if label == cluster_id and i < len(valid_indices):
                target_idx = valid_indices[i]
                targets_in_cluster.append(target_idx)
        
        if targets_in_cluster:
            expected_color = colors[cluster_id]
            print(f"  簇{cluster_id} (期望颜色{expected_color}):")
            all_same_color = True
            for target_idx in targets_in_cluster:
                actual_color = cluster_colors_new.get(target_idx, None)
                print(f"    目标{target_idx}: {actual_color}")
                if actual_color != expected_color:
                    all_same_color = False
            
            if all_same_color:
                print(f"    ✓ 簇{cluster_id}颜色一致")
            else:
                print(f"    ✗ 簇{cluster_id}颜色不一致")
    
    print("\n=== 测试完成 ===")

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    # 测试包含噪声点的情况（DBSCAN）
    print("\n--- 测试DBSCAN噪声点处理 ---")
    targets_info = [
        {'pixel_x': 100, 'pixel_y': 100, 'target_id': 0},
        {'pixel_x': 110, 'pixel_y': 105, 'target_id': 1},
        {'pixel_x': 500, 'pixel_y': 400, 'target_id': 2},  # 噪声点
    ]
    
    cluster_result = {
        'cluster_labels': [0, 0, -1],  # -1表示噪声点
        'cluster_colors': [(255, 0, 0)],  # 只有一个有效簇
        'valid_indices': [0, 1, 2],
        'algorithm': 'DBSCAN',
        'noise_points': [2]
    }
    
    cluster_colors = {}
    cluster_labels = cluster_result['cluster_labels']
    colors = cluster_result['cluster_colors']
    valid_indices = cluster_result['valid_indices']
    
    for i, cluster_label in enumerate(cluster_labels):
        if i < len(valid_indices):
            target_idx = valid_indices[i]
            if target_idx < len(targets_info) and cluster_label >= 0:  # 排除噪声点(-1)
                if cluster_label < len(colors):
                    cluster_colors[target_idx] = colors[cluster_label]
                    print(f"  目标{target_idx} -> 簇{cluster_label} -> 颜色{colors[cluster_label]}")
            elif cluster_label == -1:
                print(f"  目标{target_idx} -> 噪声点，使用默认红色")
    
    print(f"有效颜色映射: {cluster_colors}")
    print(f"噪声点数量: {len(cluster_result.get('noise_points', []))}")

if __name__ == "__main__":
    test_color_mapping_logic()
    test_edge_cases()
