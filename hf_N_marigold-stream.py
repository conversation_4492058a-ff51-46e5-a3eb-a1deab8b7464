import diffusers
import torch
import cv2
import pyrealsense2 as rs
import numpy as np

'''20240613
This code is used to test the Marigold Depth and Normals Pipeline.
Try to test using D405 or usb camera.
'''

# # 连接普通相机，获取实时图像，并将视频流显示出来
# cap = cv2.VideoCapture(0)
# while True:
#     ret, frame = cap.read()
#     cv2.imshow("frame", frame)
#     if cv2.waitKey(1) & 0xFF == ord('q'):
#         break
# cap.release()
# cv2.destroyAllWindows()

image = diffusers.utils.load_image("/media/sure/D/workProject/Mushroom_new/AllNewDate_1000/2_20210615120027_color.jpg")

# normal pipeline
pipe_normal = diffusers.MarigoldNormalsPipeline.from_pretrained(
    "prs-eth/marigold-normals-lcm-v0-1", variant="fp16", torch_dtype=torch.float16
).to("cuda")
print('normal pipeline loaded')


# Configure depth and color streams
pipeline = rs.pipeline()
config = rs.config()
config.enable_stream(rs.stream.depth, 640, 360, rs.format.z16, 30)
config.enable_stream(rs.stream.color, 640, 360, rs.format.bgr8, 30)

# Start streaming 开始拍摄
pipeline.start(config)

# Create colorizer object 创建一个滤镜
colorizer = rs.colorizer()
colorizer.set_option(rs.option.color_scheme, 2.0)

'''
<15fps on 2070
'''
try:
    while True:

        # Wait for a coherent pair of frames: depth and color
        frames = pipeline.wait_for_frames()  # 获得一组帧
        color_frame = frames.get_color_frame()  # 从这组帧中得到彩色图

        # Convert images to numpy arrays 将图转换为numpy array
        color_image = np.asanyarray(color_frame.get_data())

        normals = pipe_normal(color_image)
        vis_normal = pipe_normal.image_processor.visualize_normals(normals.prediction)
        print('vis_normal shape:', vis_normal.shape)
        # normals_image = vis_normal[0]

        # Stack both images horizontally
        # 将两种图并列
        images = np.vstack((color_image))

        # Show images
        cv2.namedWindow('RealSense', cv2.WINDOW_AUTOSIZE)
        cv2.imshow('RealSense', images)
        key = cv2.waitKey(1)
        # Press esc or 'q' to close the image window
        if key & 0xFF == ord('q') or key == 27:
            cv2.destroyAllWindows()
            break
        
finally:
    pipeline.stop()
