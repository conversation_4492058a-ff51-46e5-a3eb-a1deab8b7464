'''
【240712】
在视野中随即位置生成20个点，对视野中的所有点进行Graham <PERSON>法凸包解算，将凸包的顶点按顺序连接起来绘制出凸包的边界
然后根据凸包边界点与相邻两个点形成的内角大小，按照从小到大的顺序对凸包的边界顶点进行排序
【240715】
debug: 修复了在删除点时的错误，现在可以正确地删除凸包中的点
'''

import math
import random
import cv2
import numpy as np
 
def get_start_point(points):
    """
    返回points中纵坐标最小的点的索引，如果有多个纵坐标最小的点则返回其中横坐标最小的那个
    :param points:
    :return:
    """
    min_index = 0
    n = len(points)
    for i in range(0, n):
        if points[i][1] < points[min_index][1] or (points[i][1] == points[min_index][1] and points[i][0] < points[min_index][0]):
            min_index = i
    return min_index
 
 
def sort_polar_angle_cos(points, start_point):
    """
    按照与中心点的极角进行排序，使用的是余弦的方法
    :param points: 需要排序的点
    :param start_point: 起始点
    :return:
    """
    n = len(points)
    cos_value = []
    rank = []
    norm_list = []
    for i in range(0, n):
        point_ = points[i]
        point = [point_[0]-start_point[0], point_[1]-start_point[1]]
        rank.append(i)
        norm_value = math.sqrt(point[0]*point[0] + point[1]*point[1])
        norm_list.append(norm_value)
        if norm_value == 0:
            cos_value.append(1)
        else:
            cos_value.append(point[0] / norm_value)
 
    for i in range(0, n-1):
        index = i + 1
        while index > 0:
            if cos_value[index] > cos_value[index-1] or (cos_value[index] == cos_value[index-1] and norm_list[index] > norm_list[index-1]):
                temp = cos_value[index]
                temp_rank = rank[index]
                temp_norm = norm_list[index]
                cos_value[index] = cos_value[index-1]
                rank[index] = rank[index-1]
                norm_list[index] = norm_list[index-1]
                cos_value[index-1] = temp
                rank[index-1] = temp_rank
                norm_list[index-1] = temp_norm
                index = index-1
            else:
                break
    sorted_points = []
    for i in rank:
        sorted_points.append(points[i])
 
    return sorted_points
 
 
def vector_angle(vector):
    """
    返回一个向量与向量 [1, 0]之间的夹角， 这个夹角是指从[1, 0]沿逆时针方向旋转多少度能到达这个向量
    :param vector:
    :return:
    """
    norm_ = math.sqrt(vector[0]*vector[0] + vector[1]*vector[1])
    if norm_ == 0:
        return 0
 
    angle = math.acos(vector[0]/norm_)
    if vector[1] >= 0:
        return angle
    else:
        return 2*math.pi - angle
 
 
def coss_multi(v1, v2):
    """
    计算两个向量的叉乘
    :param v1:
    :param v2:
    :return:
    """
    return v1[0]*v2[1] - v1[1]*v2[0]
 
 
def graham_scan(points):
    # print("Graham扫描法计算凸包")
    points_copy = points.copy()
    bottom_index = get_start_point(points_copy)
    bottom_point = points_copy.pop(bottom_index)
    sorted_points = sort_polar_angle_cos(points_copy, bottom_point)
 
    m = len(sorted_points)
    if m < 2:
        print("点的数量过少，无法构成凸包")
        return
 
    stack = []
    stack.append(bottom_point)
    stack.append(sorted_points[0])
    stack.append(sorted_points[1])
 
    for i in range(2, m):
        length = len(stack)
        top = stack[length-1]
        next_top = stack[length-2]
        v1 = [sorted_points[i][0]-next_top[0], sorted_points[i][1]-next_top[1]]
        v2 = [top[0]-next_top[0], top[1]-next_top[1]]
 
        while coss_multi(v1, v2) >= 0:
            stack.pop()
            length = len(stack)
            top = stack[length-1]
            next_top = stack[length-2]
            v1 = [sorted_points[i][0] - next_top[0], sorted_points[i][1] - next_top[1]]
            v2 = [top[0] - next_top[0], top[1] - next_top[1]]
 
        stack.append(sorted_points[i])
 
    return stack

# 以凸包顶点为输入，根据每一个凸包顶点与相邻两个点形成的内角从小到大的顺序对凸包的边界顶点进行排序
def sort_convex_hull(convex_hull):
    n = len(convex_hull)
    sorted_convex_hull = []
    for i in range(0, n):
        point = convex_hull[i]
        pre_point = convex_hull[i-1]
        next_point = convex_hull[(i+1)%n]
        v1 = [pre_point[0]-point[0], pre_point[1]-point[1]]
        v2 = [next_point[0]-point[0], next_point[1]-point[1]]
        angle = vector_angle(v2) - vector_angle(v1)
        if angle < 0:
            angle += 2*math.pi
        sorted_convex_hull.append([point, angle])
 
    sorted_convex_hull.sort(key=lambda x: x[1], reverse=True)
    result = []
    for item in sorted_convex_hull:
        result.append(item[0])
 
    return result

# 计算两点之间的距离
def distance(point1, point2):
    return np.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)

# 检查新点是否与现有点保持足够的距离
def is_far_enough(new_point, points, min_distance):
    for point in points:
        if distance(new_point, point) < min_distance:
            return False
    return True

# 生成不相交的圆的中心点
def generate_points(num_points, img_width, img_height, max_radius, min_radius):
    points = []
    attempts = 0
    max_attempts = num_points * 100
    min_distance = 2 * max_radius
    while len(points) < num_points and attempts < max_attempts:
        if attempts > max_attempts // 2 and min_distance > max_radius:
            min_distance -= 1
        x = random.randint(max_radius, img_width - max_radius)
        y = random.randint(max_radius, img_height - max_radius)
        radius = random.randint(min_radius, max_radius)
        new_point = (x, y, radius)
        if is_far_enough(new_point, points, min_distance):
            points.append(new_point)
        attempts += 1
    return points

def draw_and_save(points, step):
    '''
    在此可视化方法中计算凸包并绘制结果
    '''
    img = np.zeros((img_height, img_width, 3), np.uint8)
    img.fill(255)  # 填充白色

    for point in points:
        cv2.circle(img, (point[0], point[1]), 3, (0, 0, 0), -1)  # 用黑色绘制点
        cv2.circle(img, (point[0], point[1]), point[2], (0, 0, 0))  # 使用存储的半径绘制黑色圆

    sorted_result = []  # 在条件不满足时返回空列表
    if len(points) > 2:  # 凸包至少需要3个点
        # print(f"Step {step} before: {len(points)} points")
        result = graham_scan(points)
        # print(f"Step {step} after: {len(points)} points")
        length = len(result)
        for i in range(length-1):
            cv2.line(img, (result[i][0], result[i][1]), (result[i+1][0], result[i+1][1]), (0, 0, 255), 2)
        cv2.line(img, (result[0][0], result[0][1]), (result[-1][0], result[-1][1]), (0, 0, 255), 2)

        sorted_result = sort_convex_hull(result)
        for i, point in enumerate(sorted_result):
            cv2.putText(img, str(i+1), (point[0], point[1]), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)

    if len(sorted_result) > 1:
        cv2.imwrite(f'results_convex_plan/convex_hull_step_{step}.png', img)
    
    return sorted_result  # 返回排序后的结果以便于删除


def convexPlan_end2end(points, img_width, img_height, output_filename="convex_hull_end2end.png"):
    '''
    端到端排序函数：通过多轮凸包计算和点的移除来确定最终排序
    每次计算凸包并按内角排序，移除第一个点并赋予其最终序号
    最后输出一张包含所有点最终序号的图片
    '''
    # 深拷贝points以避免修改原始数据
    points_copy = [point for point in points]
    original_points = [point for point in points]  # 保存原始点位置用于最终绘制
    final_order = {}  # 存储每个点的最终序号
    current_order = 1
    
    # 进行n-2轮排序（当点数<=2时停止）
    while len(points_copy) > 2:
        # 计算当前点集的凸包
        if len(points_copy) > 2:
            convex_hull = graham_scan(points_copy)
            if convex_hull and len(convex_hull) > 2:
                # 对凸包顶点按内角排序
                sorted_hull = sort_convex_hull(convex_hull)
                if sorted_hull:
                    # 获取排序第一的点
                    first_point = sorted_hull[0]
                    
                    # 在原始点集中找到对应的点并记录其最终序号
                    for orig_point in original_points:
                        if orig_point[:2] == first_point[:2]:  # 比较x,y坐标
                            final_order[orig_point] = current_order
                            break
                    
                    # 从当前点集中移除这个点
                    index_to_remove = next((i for i, point in enumerate(points_copy) 
                                          if point[:2] == first_point[:2]), None)
                    if index_to_remove is not None:
                        del points_copy[index_to_remove]
                    
                    current_order += 1
        else:
            break
    
    # 为剩余的点（如果有的话）分配序号
    for remaining_point in points_copy:
        for orig_point in original_points:
            if orig_point[:2] == remaining_point[:2]:
                final_order[orig_point] = current_order
                current_order += 1
                break
    
    # 创建最终的可视化图像
    img = np.zeros((img_height, img_width, 3), np.uint8)
    img.fill(255)  # 填充白色
    
    # 绘制所有原始点和圆
    for point in original_points:
        cv2.circle(img, (point[0], point[1]), 3, (0, 0, 0), -1)  # 用黑色绘制点
        cv2.circle(img, (point[0], point[1]), point[2], (0, 0, 0))  # 使用存储的半径绘制黑色圆
    
    # 绘制最终的凸包（使用原始所有点）
    if len(original_points) > 2:
        final_convex_hull = graham_scan(original_points)
        if final_convex_hull and len(final_convex_hull) > 2:
            length = len(final_convex_hull)
            for i in range(length-1):
                cv2.line(img, (final_convex_hull[i][0], final_convex_hull[i][1]), 
                        (final_convex_hull[i+1][0], final_convex_hull[i+1][1]), (0, 0, 255), 2)
            cv2.line(img, (final_convex_hull[0][0], final_convex_hull[0][1]), 
                    (final_convex_hull[-1][0], final_convex_hull[-1][1]), (0, 0, 255), 2)
    
    # 在每个点上标注最终序号
    for point in original_points:
        if point in final_order:
            cv2.putText(img, str(final_order[point]), (point[0], point[1]), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
    
    # 保存图像
    cv2.imwrite(f'results_convex_plan/{output_filename}', img)
    
    return final_order


if __name__ == "__main__":
    img_width, img_height = 560, 400
    min_radius = 25  # 最小半径
    max_radius = 50  # 最大半径
    num_points = 30  # 点的数量
    
    # 创建一个空白的图像
    img = np.zeros((img_height, img_width, 3), np.uint8)
    img.fill(255)  # 填充白色

    points = generate_points(num_points, img_width, img_height, max_radius, min_radius)
    
    # 方法1：原有的逐步保存方法
    points_copy1 = [point for point in points]  # 为原有方法创建副本
    for step in range(len(points_copy1), 2, -1):
        sorted_result = draw_and_save(points_copy1, step)
        if sorted_result:
            # 找到sorted_result中第一位点在points列表中的确切匹配，并删除
            first_point = sorted_result[0]
            # 使用enumerate找到第一个完全匹配的点的索引
            index_to_remove = next((i for i, point in enumerate(points_copy1) if point[:2] == first_point[:2]), None)
            if index_to_remove is not None:
                # 删除该索引处的点
                # print(f"Removing point at index {index_to_remove}")
                del points_copy1[index_to_remove]
    
    # 方法2：新的端到端排序方法
    print("生成端到端排序结果...")
    final_order = convexPlan_end2end(points, img_width, img_height, "convex_hull_end2end_result.png")
    print("排序完成，最终序号分配：")
    for i, point in enumerate(points):
        if point in final_order:
            print(f"点 ({point[0]}, {point[1]}) -> 序号 {final_order[point]}")
    
    
    
    # for point in points:
    #     cv2.circle(img, (point[0], point[1]), 3, (0, 0, 0), -1)  # 用黑色绘制点
    #     radius = random.randint(min_radius, max_radius)
    #     cv2.circle(img, (point[0], point[1]), radius, (0, 0, 0), 1)  # 用黑色绘制圆

    # result = graham_scan(points)

    # length = len(result)
    # for i in range(0, length-1):
    #     cv2.line(img, (result[i][0], result[i][1]), (result[i+1][0], result[i+1][1]), (0, 0, 255), 2)  # 用红色绘制线
    # cv2.line(img, (result[0][0], result[0][1]), (result[length-1][0], result[length-1][1]), (0, 0, 255), 2)  # 闭合图形

    # # 对凸包的边界顶点进行排序，并将序号打印在图像上
    # sorted_result = sort_convex_hull(result)
    # for i in range(0, length):
    #     cv2.putText(img, str(i+1), (sorted_result[i][0], sorted_result[i][1]), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
    
    # cv2.imshow('Convex Hull', img)
    # cv2.waitKey(0)
    # cv2.destroyAllWindows()