'''
已知图像上某处(w,h)点的表面法线为[x,y,z],且每个通道的取值范围为[-1, 1]
在此点(w,h)处根据法向量绘制一个立体的箭头，箭头的方向为法线的方向，比如：[0,0,1]时法线指向观察者，故箭头没有长度；[1,0,0]时法线指向右侧，箭头长度为1，[0,1,0]时法线指向上方，箭头长度为1。
0626测试通过-直观上合理
0711debug-箭头绘制有问题
'''

import diffusers
import torch
import cv2
import pyrealsense2 as rs
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

def draw_arrow(image, normal, w, h):
    '''
    image: 原图
    normal: 表面法线
    w, h: 箭头绘制的位置
    '''
    # 箭头的长度
    length = np.linalg.norm(normal)
    print('length: ', length)
    # 箭头的方向
    normal = normal / length
    print('normal: ', normal)
    # 箭头的位置
    x = h
    y = w
    z = 0
    # 绘制箭头
    fig = plt.figure()
    ax = fig.add_subplot(111, projection='3d')
    ax.quiver(x, y, z, normal[0], normal[1], normal[2], length=length)
    ax.set_xlim(0, 480)
    ax.set_ylim(0, 768)
    ax.set_zlim(0, 1)
    plt.show()



# 读入原图
image = cv2.imread("2_20210615120027_color.jpg")
image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
print('image shape: ', image.shape)

# # 读入原图
# image = diffusers.utils.load_image("2_20210615120027_color.jpg")

# 计算表面法线
pipe_normal = diffusers.MarigoldNormalsPipeline.from_pretrained(
    "prs-eth/marigold-normals-lcm-v0-1", variant="fp16", torch_dtype=torch.float16
).to("cuda")
print('normal pipeline loaded')
normals = pipe_normal(image)

# 查看normals
print('normals: ', normals.prediction.shape)  # (1, 480, 768, 3)

# 画箭头
draw_arrow(image, normals.prediction[0, 100, 100], 100, 100)
draw_arrow(image, normals.prediction[0, 200, 200], 200, 200)
draw_arrow(image, normals.prediction[0, 300, 300], 300, 300)