'''
已知原图image和表面法线图normals，normals尺寸为(W,H,3)，其三个通道的取值范围为[-1, 1]
以image为底图，将从image中稀疏采样的10x10个坐标点的法线绘制在image上
比如(0,0,1)的法线指向观察者，故箭头没有长度；(1,0,0)的法线指向右侧，箭头长度为1，(0,1,0)的法线指向上方，箭头长度为1。
0711debug通过
'''

import diffusers
import torch
import cv2
import pyrealsense2 as rs
import numpy as np
    
imgPath = "2/1_20210615124242_color.jpg"

# 计算表面法线图
image = diffusers.utils.load_image(imgPath)
pipe_normal = diffusers.MarigoldNormalsPipeline.from_pretrained(
    "prs-eth/marigold-normals-lcm-v0-1", variant="fp16", torch_dtype=torch.float16
).to("cuda")
# Returns:
    # [`~pipelines.marigold.MarigoldNormalsOutput`] or `tuple`，where the first element is the prediction, the second element is the uncertainty
    # (or `None`), and the third is the latent (or `None`).
normals = pipe_normal(image)
# print('normals: ', normals.prediction.shape)  # (1, 480, 768, 3)
# 可视化：maps the three-dimensional prediction with pixel values in the range [-1, 1] into an RGB image
#  Conceptually, each pixel is painted according to the surface normal vector in the frame of reference, 
#  where X axis points right, Y axis points up, and Z axis points at the viewer.
#  z轴的-1即指向远离观察者的方向，怀疑是类似平面凹进去的地方？
vis_normal = pipe_normal.image_processor.visualize_normals(normals.prediction)
vis_normal[0].save("2/marigoldNormalViz.png")
# the surface normal vector points straight at the viewer, meaning that its coordinates are [0, 0, 1]. This vector maps to the RGB [128, 128, 255].
# Similarly, a surface normal on the cheek in the right part of the image has a large X component, which increases the red hue. 
# Points on the shoulders pointing up with a large Y promote green color.

# 计算深度图
pipe_depth = diffusers.MarigoldDepthPipeline.from_pretrained(
    "prs-eth/marigold-depth-lcm-v1-0", variant="fp16", torch_dtype=torch.float16
).to("cuda")
depth = pipe_depth(image)
vis_depth = pipe_depth.image_processor.visualize_depth(depth.prediction)  # matplotlib’s colormaps (Spectral by default
vis_depth[0].save("2/marigoldDepthViz.png")

# 读取原图
image = cv2.imread(imgPath)
normals = normals.prediction[0]

# 从原图中稀疏采样50x50个点，并计算相应位置处法线的终点坐标，记录在列表中
points = []
scale = 30
for i in range(0, 480, 9):
    for j in range(0, 768, 15):
        # 从法线图中获取法线
        normal = normals[i, j]
        # 计算法线的终点坐标
        x = j
        y = i
        z = 0
        length = np.linalg.norm(normal)
        normal = normal / length
        x_end = x + normal[0] * scale
        y_end = y + normal[1] * scale * (-1)
        z_end = z + normal[2] * scale
        points.append([(x, y, z), (x_end, y_end, z_end)])

# 使cv2的方法，根据points中的坐标把所有的箭头绘制在原图上，箭头本身为红色，起点为绿色，终点为黄色
for point in points:
    # 箭头起点
    cv2.circle(image, (int(point[0][0]), int(point[0][1])), 1, (0, 255, 0), -1)
    # # 箭头终点
    # cv2.circle(image, (int(point[1][0]), int(point[1][1])), 1, (0, 255, 255), -1)
    # 箭头
    cv2.arrowedLine(image, (int(point[0][0]), int(point[0][1])), (int(point[1][0]), int(point[1][1])), (0, 0, 255), 1)
# 将图保存到本地
cv2.imwrite("2/normalsArrow_-y.jpg", image)