import diffusers
import torch
import torch
print("CUDA available:", torch.cuda.is_available())
print("CUDA devices:", torch.cuda.device_count())
print("Current CUDA device:", torch.cuda.current_device())
import cv2
import numpy as np

'''20240626
尝试将normals图可视化
'''

image = diffusers.utils.load_image("Datasets/mushroom_dev/mycionics装备褐菇.png")
print('image loaded')

# 表面法线估计normal pipeline
pipe_normal = diffusers.MarigoldNormalsPipeline.from_pretrained(
    "prs-eth/marigold-normals-lcm-v0-1", variant="fp16", torch_dtype=torch.float16, local_files_only=True
).to("cuda")
print('normal pipeline loaded on: ', pipe_normal.device)
'''
call的Returns:
    [`~pipelines.marigold.MarigoldNormalsOutput`] or `tuple`，where the first element is the prediction, the second element is the uncertainty
    (or `None`), and the third is the latent (or `None`).
'''
normals = pipe_normal(image)

# 查看normals
print('normals: ', normals.prediction.shape)  # (1, 480, 768, 3)

'''
可视化：maps the three-dimensional prediction with pixel values in the range [-1, 1] into an RGB image
Conceptually, each pixel is painted according to the surface normal vector in the frame of reference, 
where X axis points right, Y axis points up, and Z axis points at the viewer.
z轴的-1即指向远离观察者的方向，怀疑是类似平面凹进去的地方？
'''
vis_normal = pipe_normal.image_processor.visualize_normals(normals.prediction)
vis_normal[0].save("dev-utils/results/marigold_normal_rgb.png")
'''
the surface normal vector points straight at the viewer, meaning that its coordinates are [0, 0, 1]. This vector maps to the RGB [128, 128, 255].
Similarly, a surface normal on the cheek in the right part of the image has a large X component, which increases the red hue. 
Points on the shoulders pointing up with a large Y promote green color.
'''