#!/usr/bin/env python3
"""
测试基于凸包的路径规划功能
"""

import cv2
import numpy as np
import random
import sys
import os

# 添加mushroom_4th目录到路径中
sys.path.append('/home/<USER>/dev-utils/mushroom_4th')

# 导入凸包路径规划相关函数
from vision_core import (
    graham_scan, sort_convex_hull, convex_path_planning,
    get_start_point, sort_polar_angle_cos, vector_angle, coss_multi
)

def generate_test_dets(num_points=8, img_width=560, img_height=400):
    """生成测试用的检测结果数据"""
    dets = []
    for _ in range(num_points):
        x = random.randint(50, img_width - 50)
        y = random.randint(50, img_height - 50)
        size = random.uniform(30, 80)
        # 格式: [center_x, center_y, z, rx, ry, size]
        dets.append([x, y, 0.0, 0.0, 0.0, size])
    return dets

def create_test_image(dets, img_width=560, img_height=400):
    """创建测试图像，在检测点位置绘制圆圈"""
    img = np.zeros((img_height, img_width, 3), np.uint8)
    img.fill(255)  # 白色背景
    
    # 绘制所有检测点
    for i, det in enumerate(dets):
        center = (int(det[0]), int(det[1]))
        radius = int(det[5] / 3)  # 将size转换为合适的半径
        
        # 绘制圆圈
        cv2.circle(img, center, radius, (100, 100, 100), 2)
        # 绘制中心点
        cv2.circle(img, center, 3, (0, 0, 255), -1)
        # 标注原始序号
        cv2.putText(img, f'#{i+1}', (center[0] - 15, center[1] - radius - 5),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
    
    return img

def test_convex_planning():
    """测试凸包路径规划功能"""
    print("=== 测试凸包路径规划功能 ===")
    
    # 生成测试数据
    img_width, img_height = 560, 400
    num_points = 8
    
    # 生成随机测试点
    dets = generate_test_dets(num_points, img_width, img_height)
    
    print(f"生成{len(dets)}个测试点:")
    for i, det in enumerate(dets):
        print(f"  点{i+1}: ({det[0]:.1f}, {det[1]:.1f}), size={det[5]:.1f}")
    
    # 创建测试图像
    test_img = create_test_image(dets, img_width, img_height)
    
    # 保存原始图像
    original_path = '/home/<USER>/dev-utils/mushroom_4th/results_images/test_original.jpg'
    cv2.imwrite(original_path, test_img)
    print(f"原始测试图像保存到: {original_path}")
    
    # 执行凸包路径规划
    planning_path = '/home/<USER>/dev-utils/mushroom_4th/results_images/test_planning.jpg'
    try:
        ordered_dets, final_order = convex_path_planning(dets, test_img, planning_path)
        
        print(f"\n路径规划结果:")
        print(f"  规划前点数: {len(dets)}")
        print(f"  规划后点数: {len(ordered_dets)}")
        
        print(f"\n最终访问顺序:")
        for i, det in enumerate(ordered_dets):
            center = (int(det[0]), int(det[1]))
            if center in final_order:
                print(f"  访问序号{final_order[center]}: 点({center[0]}, {center[1]}), size={det[5]:.1f}")
        
        return True
        
    except Exception as e:
        print(f"路径规划执行出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_individual_functions():
    """测试各个凸包算法函数"""
    print("\n=== 测试各个凸包算法函数 ===")
    
    # 测试点集
    test_points = [(100, 100), (200, 50), (300, 100), (250, 200), (150, 250), (50, 200)]
    print(f"测试点集: {test_points}")
    
    # 测试凸包计算
    try:
        convex_hull = graham_scan(test_points)
        print(f"凸包结果: {convex_hull}")
        
        if len(convex_hull) > 2:
            sorted_hull = sort_convex_hull(convex_hull)
            print(f"按内角排序的凸包: {sorted_hull}")
        
        return True
    except Exception as e:
        print(f"凸包算法测试出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 确保结果目录存在
    os.makedirs('/home/<USER>/dev-utils/mushroom_4th/results_images', exist_ok=True)
    
    # 测试各个函数
    success1 = test_individual_functions()
    success2 = test_convex_planning()
    
    if success1 and success2:
        print("\n=== 所有测试通过！ ===")
    else:
        print("\n=== 部分测试失败 ===")
