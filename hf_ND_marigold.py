import diffusers
import torch
import cv2
import numpy as np

'''20240626
This code is used to test the Marigold Depth and Normals Pipeline.
Try to test single image.

load the LCM checkpoint, perform just one denoising diffusion step,
pipe(image) call completes in 280ms on RTX 3090 GPU.
Internally, the input image is encoded with the Stable Diffusion VAE encoder, 
then the U-Net performs one denoising step, and finally, the prediction latent is decoded with the VAE decoder into pixel space. 
marigold在huggingface的核心用法

20250319
将cache下的文件复制到wsl2中，尝试运行此代码
'''

image = diffusers.utils.load_image('Datasets/mushroom_val/2_20210615123043_color.jpg')
print('image loaded: ', image.shape)

# 单目深度估计depth pipeline
pipe_depth = diffusers.MarigoldDepthPipeline.from_pretrained(
    "prs-eth/marigold-depth-lcm-v1-0", variant="fp16", torch_dtype=torch.float16
).to("cuda")
print('depth pipeline loaded')

# 可以通过将流程所使用的vae模块更换为轻量化的模型以获得更快的推理速度，下同
# pipe_depth.vae = diffusers.AutoencoderTiny.from_pretrained(
#      "madebyollin/taesd", torch_dtype=torch.float16
#  ).cuda()
# 进一步挤压在某些硬件平台上可能的速度
# pipe_depth.unet = torch.compile(pipe_depth.unet, mode="reduce-overhead", fullgraph=True)

# print('depth pipeline loaded')
depth = pipe_depth(image)

# 可视化：map the predicted pixel values from a single-channel [0, 1] depth range into an RGB image
vis_depth = pipe_depth.image_processor.visualize_depth(depth.prediction)  # matplotlib’s colormaps (Spectral by default
vis_depth[0].save(f"dev-utils/results/marigold_depth_rgb.png")

# The 16-bit PNG file stores the single channel values mapped linearly from the [0, 1] range into [0, 65535]. 
vis_depth16bit = pipe_depth.image_processor.export_depth_to_16bit_png(depth.prediction)
vis_depth16bit[0].save(f"dev-utils/results/marigold_depth_grey.png")



# 表面法线估计normal pipeline
pipe_normal = diffusers.MarigoldNormalsPipeline.from_pretrained(
    "prs-eth/marigold-normals-lcm-v0-1", variant="fp16", torch_dtype=torch.float16
).to("cuda")
print('normal pipeline loaded')
normals = pipe_normal(image)

# 可视化：maps the three-dimensional prediction with pixel values in the range [-1, 1] into an RGB image
# Conceptually, each pixel is painted according to the surface normal vector in the frame of reference, 
# where X axis points right, Y axis points up, and Z axis points at the viewer.
# z轴的-1即指向远离观察者的方向，怀疑是类似平面凹进去的地方？
vis_normal = pipe_normal.image_processor.visualize_normals(normals.prediction)
vis_normal[0].save(f"dev-utils/results/marigold_normals_rgb.png")
# the surface normal vector points straight at the viewer, meaning that its coordinates are [0, 0, 1]. This vector maps to the RGB [128, 128, 255].
# Similarly, a surface normal on the cheek in the right part of the image has a large X component, which increases the red hue. 
# Points on the shoulders pointing up with a large Y promote green color.