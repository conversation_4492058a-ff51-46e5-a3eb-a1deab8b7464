#!/usr/bin/env python3
"""
演示在vision_core.py中使用凸包路径规划的完整流程
不依赖YOLO模型，使用模拟的检测结果
"""

import cv2
import numpy as np
import random
import sys
import os

# 添加mushroom_4th目录到路径中
sys.path.append('/home/<USER>/dev-utils/mushroom_4th')

from vision_core import convex_path_planning

def simulate_pickPoints(imgrgb, savePath, num_objects=6):
    """
    模拟pickPoints函数的行为，生成模拟的检测结果
    """
    print(f"-----模拟检测到 {num_objects} 个目标-----\n")
    
    dets = []
    result_image = imgrgb.copy()
    
    # 生成随机的检测结果
    for i in range(num_objects):
        # 随机生成中心点位置
        center_x = random.randint(50, imgrgb.shape[1] - 50)
        center_y = random.randint(50, imgrgb.shape[0] - 50)
        size = random.uniform(30, 80)
        
        # 在图像上绘制检测结果
        center = (center_x, center_y)
        radius = int(size / 3)
        
        # 绘制轮廓
        cv2.circle(result_image, center, radius, (0, 0, 255), 2)
        # 绘制中心点
        cv2.circle(result_image, center, 3, (0, 0, 255), -1)
        # 标注原始序号
        cv2.putText(result_image, f'Det{i+1}', (center[0] + 10, center[1] - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
        
        # 添加到检测结果列表，格式: [center_x, center_y, z, rx, ry, size]
        dets.append([center_x, center_y, 0.0, 0.0, 0.0, size])
    
    print('=====模拟检测完成=====')
    
    # 路径规划
    if len(dets) >= 3:
        # 生成路径规划可视化图片路径
        planning_savePath = savePath.replace('.jpg', '_planning.jpg') if savePath.endswith('.jpg') else savePath + '_planning.jpg'
        aft_dets, final_order = convex_path_planning(dets, result_image, planning_savePath)
        print(f'-----凸包路径规划完成，共{len(aft_dets)}个目标点按序排列-----')
    else:
        aft_dets = dets
        print(f'-----目标点少于3个，跳过凸包路径规划，共{len(aft_dets)}个目标点-----')

    print('-----Output {} objects in image'.format(len(aft_dets)))

    cv2.imwrite(savePath, result_image)

    return aft_dets

def main():
    """主函数演示完整流程"""
    print("=== 演示基于凸包的路径规划功能 ===\n")
    
    # 创建模拟图像
    img_width, img_height = 640, 480
    color_image = np.ones((img_height, img_width, 3), dtype=np.uint8) * 255  # 白色背景
    
    # 添加一些背景纹理使图像更真实
    for _ in range(20):
        x = random.randint(0, img_width)
        y = random.randint(0, img_height)
        cv2.circle(color_image, (x, y), random.randint(2, 8), (240, 240, 240), -1)
    
    # 设置保存路径
    tempDir = '/home/<USER>/dev-utils/mushroom_4th/results_images/'
    os.makedirs(tempDir, exist_ok=True)
    result_imagePath = tempDir + 'demo_convex_planning_vis.jpg'
    
    print(f"处理模拟图像，保存结果到: {result_imagePath}")
    
    # 执行模拟的检测和路径规划
    aft_dets = simulate_pickPoints(color_image, result_imagePath, num_objects=8)
    
    print(f"\n=== 最终结果 ===")
    print(f"按路径规划顺序的目标点列表:")
    for i, det in enumerate(aft_dets):
        print(f"  {i+1}. 位置: ({det[0]:.1f}, {det[1]:.1f}), 尺寸: {det[5]:.1f}")
    
    print(f"\n结果图像已保存到:")
    print(f"  原始检测结果: {result_imagePath}")
    planning_path = result_imagePath.replace('.jpg', '_planning.jpg')
    print(f"  路径规划结果: {planning_path}")

if __name__ == "__main__":
    main()
