import diffusers
import torch
import cv2
import numpy as np
from ultralytics import Y<PERSON><PERSON>
from scipy.ndimage import label
from scipy.spatial.distance import cdist

'''
使用YOLOv8-seg模型对蘑菇RGB图进行分割，得到分割结果
使用Marigold模型对蘑菇RGB图进行法线估计，得到法线图
对每个蘑菇mask区域计算法线方向的变化速率，找到mask区域内法线方向变化较小的区域，并可视化
# Segmentation
result.masks.data      # masks, (N, H, W)
result.masks.xy        # x,y segments (pixels), List[segment] * N
result.masks.xyn       # x,y segments (normalized), List[segment] * N
# Normals
与RGB图对齐的三通道法线图normal为(W,H,3)，其三个通道的取值范围为[-1, 1]

【250321】debug通过
'''

def calculate_normal_variation(normals, mask):
    """Calculate normal direction variation within a mask region"""
    # Convert normals to unit vectors
    normals_norm = np.linalg.norm(normals, axis=2, keepdims=True)
    normals_unit = normals / (normals_norm + 1e-6)
    
    # Convert mask to boolean type
    mask = mask.astype(bool)
    
    # Get masked region coordinates
    y_coords, x_coords = np.where(mask)
    if len(y_coords) == 0:
        return None, None
    
    # Get normal vectors in masked region
    masked_normals = normals_unit[mask]
    
    # Calculate pairwise cosine distances between all normal vectors
    # This gives us variation in normal direction
    distances = 1 - np.abs(np.dot(masked_normals, masked_normals.T))
    
    # Calculate mean variation for each point
    mean_variations = np.mean(distances, axis=1)
    
    # Create variation map
    variation_map = np.zeros_like(mask, dtype=float)
    variation_map[mask] = mean_variations
    
    # Debug info
    # print(f"Variation map min: {np.min(mean_variations)}, max: {np.max(mean_variations)}")
    
    return variation_map, mean_variations

def calculate_normal_variation2(normals, mask, kernel_size=50):
    """Calculate normal direction variation within local neighborhoods
    kernel_size为5时，variation图几乎没有差异；变大以后处理速度极慢fail！
    Args:
        normals: Normal vectors of shape (H, W, 3)
        mask: Boolean mask of shape (H, W)
        kernel_size: Size of the local neighborhood (odd number)
    """
    # Convert normals to unit vectors
    normals_norm = np.linalg.norm(normals, axis=2, keepdims=True)
    normals_unit = normals / (normals_norm + 1e-6)
    
    # Convert mask to boolean type
    mask = mask.astype(bool)
    
    # Get image dimensions
    H, W = mask.shape
    
    # Create variation map
    variation_map = np.zeros_like(mask, dtype=float)
    
    # Calculate padding size
    pad = kernel_size // 2
    
    # Pad the normals and mask
    normals_padded = np.pad(normals_unit, ((pad,pad), (pad,pad), (0,0)), mode='reflect')
    mask_padded = np.pad(mask, ((pad,pad), (pad,pad)), mode='constant', constant_values=False)
    
    # For each point in the mask
    for y in range(pad, H + pad):
        for x in range(pad, W + pad):
            if not mask_padded[y, x]:
                continue
                
            # Extract local neighborhood
            local_normals = normals_padded[y-pad:y+pad+1, x-pad:x+pad+1]
            local_mask = mask_padded[y-pad:y+pad+1, x-pad:x+pad+1]
            
            # Get center normal
            center_normal = local_normals[pad, pad]
            
            # Calculate variations with valid neighbors
            variations = []
            for i in range(kernel_size):
                for j in range(kernel_size):
                    if local_mask[i, j] and (i != pad or j != pad):
                        # Calculate cosine distance
                        neighbor_normal = local_normals[i, j]
                        cos_dist = 1 - np.abs(np.dot(center_normal, neighbor_normal))
                        variations.append(cos_dist)
            
            # Calculate mean variation if we have neighbors
            if variations:
                variation_map[y-pad, x-pad] = np.mean(variations)
    
    # Get mean variations for return value consistency
    mean_variations = variation_map[mask]
    
    return variation_map, mean_variations

def find_stable_region(variation_map, mask, target_ratio=0.2):
    """Find the largest connected region with minimal normal variation"""
    # 添加调试信息
    # print(f"Variation map stats in mask - min: {np.min(variation_map[mask])}, max: {np.max(variation_map[mask])}")
    
    # 使用更宽松的阈值，比如75百分位数
    threshold = np.percentile(variation_map[mask], 20)  # 改为75百分位
    stable_mask = (variation_map < threshold) & mask
    # print(f"Number of points below threshold: {np.sum(stable_mask)}")
    
    # Find connected components
    labeled_array, num_features = label(stable_mask)
    # print(f"Number of connected components: {num_features}")
    
    # Calculate areas of components
    areas = [np.sum(labeled_array == i) for i in range(1, num_features + 1)]
    if not areas:
        print("No connected components found")
        return None
    
    # 降低面积要求
    mask_area = np.sum(mask)
    target_area = mask_area * target_ratio  # 面积大小
    # print(f"Mask area: {mask_area}, Target area: {target_area}")
    # print(f"Component areas: {areas}")
    
    valid_components = [i for i, area in enumerate(areas, 1) if area >= target_area]
    if not valid_components:
        # print("No components meet size requirement")
        # 如果没有找到足够大的区域，返回最大的连通区域
        largest_component = np.argmax(areas) + 1
        return labeled_array == largest_component
    
    # Get the component with largest area
    best_component = max(valid_components, key=lambda i: areas[i-1])
    return labeled_array == best_component

def visualize_results(image, mask, stable_region, variation_map):
    """Visualize the results as a heatmap overlay"""
    # Create heatmap
    heatmap = np.zeros_like(image)
    heatmap[mask] = variation_map[mask]
    
    # Normalize heatmap
    heatmap = (heatmap - np.min(heatmap)) / (np.max(heatmap) - np.min(heatmap))
    heatmap = (heatmap * 255).astype(np.uint8)
    
    # Apply colormap
    heatmap = cv2.applyColorMap(heatmap, cv2.COLORMAP_JET)
    
    # Create overlay
    overlay = cv2.addWeighted(image, 0.7, heatmap, 0.3, 0)
    
    # Draw stable region boundary
    if stable_region is not None:
        contours, _ = cv2.findContours(stable_region.astype(np.uint8), 
                                     cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        cv2.drawContours(overlay, contours, -1, (0, 255, 0), 2)
    
    return overlay

if __name__ == '__main__':
    imgPath = "Datasets/mushroom_dev/mycionics装备褐菇.png"  # "Datasets/mushroom_val/2_20210615123635_color.jpg"
    heatPath = 'dev-utils/results/centerFind_combined褐菇3.jpg'
    weightPath = "yolov8-1/runs/segment/train30/weights/best.pt"

    net = YOLO(weightPath)
    print('-----Model init done!-----')
    # model inference
    results = net(imgPath, conf=0.7, max_det=130)[0]
    print('-----results.mask: ', len(results.masks.data))

    image = diffusers.utils.load_image(imgPath)
    image = np.array(image)
    
    # 表面法线估计normal pipeline
    pipe_normal = diffusers.MarigoldNormalsPipeline.from_pretrained(
        "prs-eth/marigold-normals-lcm-v0-1", variant="fp16", torch_dtype=torch.float16, local_files_only=True
    ).to("cuda")
    print('normal pipeline loaded on: ', pipe_normal.device)
    normals = pipe_normal(image).prediction[0]
    print('-----Normals prediction done!-----')
    
    # Create a combined heatmap for all masks
    combined_heatmap = np.zeros_like(image[:,:,0], dtype=np.float32)  # 使用float32类型
    combined_stable_regions = np.zeros_like(image[:,:,0], dtype=bool)
    
    print('process mask now!')
    # Process each mask
    for i in range(len(results.masks.data)):
        mask = results.masks.data[i].cpu().numpy().astype(bool)
        # print('process mask: ', i)
        
        variation_map, _ = calculate_normal_variation(normals, mask)
        if variation_map is None:
            print('variation map none!')
            continue
            
        # 确保variation_map也是float32类型
        variation_map = variation_map.astype(np.float32)
        
        # 使用numpy.where进行赋值
        combined_heatmap = np.where(mask, variation_map, combined_heatmap)
        
        # # Debug输出
        # print(f"Mask {i} - variation_map in mask: {np.min(variation_map[mask]):.6f} to {np.max(variation_map[mask]):.6f}")
        # print(f"Combined heatmap in mask: {np.min(combined_heatmap[mask]):.6f} to {np.max(combined_heatmap[mask]):.6f}")
        
        # Find stable region
        stable_region = find_stable_region(variation_map, mask)
        
        # Add to combined stable regions
        if stable_region is not None:
            combined_stable_regions = combined_stable_regions | stable_region
    
    # Debug check combined heatmap values
    print(f"Combined heatmap - min: {np.min(combined_heatmap)}, max: {np.max(combined_heatmap)}")
    
    # Normalize combined heatmap - make sure values are positive
    # Force positive values if needed
    combined_heatmap = combined_heatmap - np.min(combined_heatmap)  # Shift to make min = 0
    print(f"After shift - min: {np.min(combined_heatmap)}, max: {np.max(combined_heatmap)}")
    
    if np.max(combined_heatmap) > 0:
        combined_heatmap = combined_heatmap / np.max(combined_heatmap)  # Normalize to [0,1]
        # 反转值，使得法线变化小的地方值变大（显示为蓝色），变化大的地方值变小（显示为红色）
        combined_heatmap = 1 - combined_heatmap
        combined_heatmap = (combined_heatmap * 255).astype(np.uint8)
        combined_heatmap = cv2.applyColorMap(combined_heatmap, cv2.COLORMAP_JET)
        print('combined heatmap done')
        
        # Create final overlay
        final_overlay = cv2.addWeighted(image, 0.5, combined_heatmap, 0.5, 0)
        
        # Draw all stable region boundaries
        contours, _ = cv2.findContours(combined_stable_regions.astype(np.uint8), 
                                     cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        print(f"Number of contours found: {len(contours)}")
        cv2.drawContours(final_overlay, contours, -1, (0, 0, 0), 1)
        
        # Save the combined result
        cv2.imwrite(heatPath, cv2.cvtColor(final_overlay, cv2.COLOR_RGB2BGR))
        print('centerFind image saved!')
    else:
        print("ERROR: Combined heatmap has no positive values after normalization!")

    # Debug: 检查稳定区域
    print(f"Number of stable regions: {np.count_nonzero(combined_stable_regions)}")
    print(f"Stable regions found: {np.any(combined_stable_regions)}")