'''
将指定路径下的图片合成为GIF
'''

import os
import imageio.v2 as imageio

def create_gif(image_list, gif_name, duration):
    frames = []
    for image_name in image_list:
        frames.append(imageio.imread(image_name))
    imageio.mimsave(gif_name, frames, 'GIF', duration = duration)  # duration为每张图片的持续时间
    return

def main():
    image_path = r'convex_plan'
    image_list = [os.path.join(image_path, i) for i in os.listdir(image_path)]
    # 图片名格式为convex_hull_step_12.png，按照其中数字从小到大排序
    image_list.sort(key = lambda x: int(x.split('_')[-1].split('.')[0]), reverse=True)
    gif_name = 'convex_plan/convexPlan.gif'
    duration = 0.2  # 每张图片的持续时间，单位为秒
    create_gif(image_list, gif_name, duration)
    return

if __name__ == '__main__':
    main()