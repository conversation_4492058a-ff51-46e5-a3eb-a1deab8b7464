# 全局参数250408
# 【250725】wsl2本地开发调试
class cfg(object):
    # mushroom size, /m, diameter
    TSIZE =  0.035
    # resolution x
    RSLUX = 848
    RSLUY = 480
    THRESHOLD = 0.91
    DEPTH_EXPOSURE = 500.0
    COLOR_EXPOSURE = 10.0
    MODEL = 1  # 0 for Real, 1 for Total, 2 for Model
    IPURL = '************:8001'
    # NEW_TERMINAL = '/home/<USER>/Desktop/queryBattery/Bettary.py'
    YOLOPATH = "/home/<USER>/yolov8-1/runs/segment/train30/weights/best.pt"
    NORMALPATH = "prs-eth/marigold-normals-lcm-v0-1" # "GonzaloMG/marigold-e2e-ft-normals"  # "prs-eth/marigold-normals-lcm-v0-1"
    SIZEBIAS = 0.0005  # 尺寸测算偏差控制


if __name__ == '__main__':
    cfg = CFG()
    TSIZE = cfg.TSIZE
    print(TSIZE)