import diffusers
import torch
import cv2
import numpy as np
from ultralytics import YOLO
from scipy.ndimage import label, distance_transform_edt
from scipy.spatial.distance import cdist
import matplotlib.pyplot as plt
import matplotlib

'''
使用YOLOv8-seg模型对蘑菇RGB图进行分割，得到分割结果
使用Marigold模型对蘑菇RGB图进行法线估计，得到法线图
对每一个蘑菇mask区域都执行以下处理：
1. 将实例分割的mask边界用红色线可视化
2. 计算此mask区域内法线方向的变化速率，并用heatmap图可视化，法向变化大的区域用红色显示
3. 找到此mask区域内法线方向平均变化最小的一个连通区域，此连通区域占mask面积的20%，连通区域的边缘用黑色线可视化
4. 在此mask区域内找到一个安全坐标“最缓点”，此安全坐标与mask内任意点之间的像素距离与那点处的法向变化速率正相关，即mask区域内越是法向变化速率快的区域，安全坐标越是远离。

要求以上所有处理结果都可视化到一张图上。

已知
yolo模型的推理示例为：
net = YOLO(weightPath)
results = net(imgPath, conf=0.7, max_det=130)[0]
marigold模型的推理示例为：
image = diffusers.utils.load_image("imagPath")
pipe_normal = diffusers.MarigoldNormalsPipeline.from_pretrained(
    "prs-eth/marigold-normals-lcm-v0-1", variant="fp16", torch_dtype=torch.float16, local_files_only=True
).to("cuda")
normals = pipe_normal(image).prediction[0]

# Segmentation
result.masks.data      # masks, (N, H, W)
result.masks.xy        # x,y segments (pixels), List[segment] * N
result.masks.xyn       # x,y segments (normalized), List[segment] * N
# Normals
与RGB图对齐的三通道法线图normal为(W,H,3)，其三个通道的取值范围为[-1, 1]
'''

def load_models(yolo_path, marigold_path="prs-eth/marigold-normals-lcm-v0-1"):
    """加载YOLO和Marigold模型"""
    # 加载YOLO模型
    yolo_model = YOLO(yolo_path)
    
    # 加载Marigold模型
    pipe_normal = diffusers.MarigoldNormalsPipeline.from_pretrained(
        marigold_path, variant="fp16", torch_dtype=torch.float16, local_files_only=True
    ).to("cuda")
    
    return yolo_model, pipe_normal

def calculate_normal_gradient0(normal_map, mask):
    """计算mask区域内法线图的变化速率，使用余弦距离计算法线向量方向的变化
    
    Args:
        normal_map: 法线图，形状为(H, W, 3)
        mask: 要计算的区域掩码，形状为(H, W)，布尔类型或二值类型
    
    Returns:
        gradient_magnitude: 法线变化速率图，仅在mask区域内有值，形状为(H, W)
    """
    # 转换为numpy数组以便于操作
    normal_np = normal_map.numpy() if isinstance(normal_map, torch.Tensor) else normal_map
    
    # 确保mask是布尔类型
    mask = mask.astype(bool)
    
    # 创建全零的变化速率图
    H, W, _ = normal_np.shape
    gradient_magnitude = np.zeros((H, W), dtype=float)
    
    # 如果mask为空，直接返回全零图
    if not np.any(mask):
        return gradient_magnitude
    
    # 将法线向量标准化为单位向量
    normals_norm = np.linalg.norm(normal_np, axis=2, keepdims=True)
    normals_unit = normal_np / (normals_norm + 1e-6)
    
    # 获取mask内部的坐标和法线向量
    y_coords, x_coords = np.where(mask)
    if len(y_coords) == 0:
        return gradient_magnitude
    
    # 提取mask区域内的法线向量
    masked_normals = []
    masked_positions = []
    for y, x in zip(y_coords, x_coords):
        masked_normals.append(normals_unit[y, x])
        masked_positions.append((y, x))
    
    masked_normals = np.array(masked_normals)
    
    # 计算掩码区域内所有法线向量之间的余弦距离
    cosine_sim = np.dot(masked_normals, masked_normals.T)
    # 确保值在[-1, 1]范围内
    cosine_sim = np.clip(cosine_sim, -1.0, 1.0)
    # 计算余弦距离
    distances = 1 - np.abs(cosine_sim)
    
    # 计算每个点的平均变化率
    mean_variations = np.mean(distances, axis=1)
    
    # 将计算结果填入变化速率图
    for (y, x), var in zip(masked_positions, mean_variations):
        gradient_magnitude[y, x] = var
    
    # 仅在mask区域内进行归一化
    mask_min = np.min(mean_variations)
    mask_max = np.max(mean_variations)
    if mask_max > mask_min:
        normalized_variations = (mean_variations - mask_min) / (mask_max - mask_min)
        
        # 填回归一化后的值
        for idx, (y, x) in enumerate(masked_positions):
            gradient_magnitude[y, x] = normalized_variations[idx]
    
    return gradient_magnitude

def calculate_normal_gradient(normal_map, mask, neighborhood_radius=10):
    """计算mask区域内法线图的变化速率，使用局部邻域内法线向量的余弦距离
    
    Args:
        normal_map: 法线图，形状为(H, W, 3)
        mask: 要计算的区域掩码，形状为(H, W)，布尔类型或二值类型
        neighborhood_radius: 计算局部变化率的邻域半径，默认5像素
        
    Returns:
        gradient_magnitude: 法线变化速率图，仅在mask区域内有值，形状为(H, W)
    """
    # 转换为numpy数组以便于操作
    normal_np = normal_map.numpy() if isinstance(normal_map, torch.Tensor) else normal_map
    
    # 确保mask是布尔类型
    mask = mask.astype(bool)
    
    # 创建全零的变化速率图
    H, W, _ = normal_np.shape
    gradient_magnitude = np.zeros((H, W), dtype=float)
    
    # 如果mask为空，直接返回全零图
    if not np.any(mask):
        return gradient_magnitude
    
    # 将法线向量标准化为单位向量
    normals_norm = np.linalg.norm(normal_np, axis=2, keepdims=True)
    normals_unit = normal_np / (normals_norm + 1e-6)
    
    # 获取mask内部的坐标
    y_coords, x_coords = np.where(mask)
    
    # 对mask内的每个点计算局部邻域内的变化率
    for i, (y, x) in enumerate(zip(y_coords, x_coords)):
        # 当前点的法线向量
        current_normal = normals_unit[y, x]
        
        # 定义邻域范围
        y_min = max(0, y - neighborhood_radius)
        y_max = min(H - 1, y + neighborhood_radius)
        x_min = max(0, x - neighborhood_radius)
        x_max = min(W - 1, x + neighborhood_radius)
        
        # 提取局部窗口
        local_window = mask[y_min:y_max+1, x_min:x_max+1]
        local_normals = normals_unit[y_min:y_max+1, x_min:x_max+1]
        
        # 计算局部窗口内所有mask内点与当前点的余弦距离
        # 排除当前点自身位置
        current_y_local, current_x_local = y - y_min, x - x_min
        
        # 创建一个同样大小的mask来排除当前点
        exclude_self = np.ones_like(local_window, dtype=bool)
        if 0 <= current_y_local < exclude_self.shape[0] and 0 <= current_x_local < exclude_self.shape[1]:
            exclude_self[current_y_local, current_x_local] = False
        
        # 只考虑局部窗口内且在mask内且不是自身的点
        valid_points = local_window & exclude_self
        
        if np.any(valid_points):
            # 展平有效点的坐标和对应的法线向量
            flat_indices = np.where(valid_points.flatten())[0]
            valid_normals = local_normals.reshape(-1, 3)[flat_indices]
            
            # 计算余弦相似度
            cosine_sims = np.dot(valid_normals, current_normal)
            cosine_sims = np.clip(cosine_sims, -1.0, 1.0)
            
            # 计算余弦距离
            distances = 1 - np.abs(cosine_sims)
            
            # 计算平均变化率
            gradient_magnitude[y, x] = np.mean(distances)
    
    # 仅在mask区域内进行归一化
    mask_values = gradient_magnitude[mask]
    mask_min = np.min(mask_values)
    mask_max = np.max(mask_values)
    
    if mask_max > mask_min:
        # 归一化到[0, 1]范围
        normalized_values = (mask_values - mask_min) / (mask_max - mask_min)
        
        # 将归一化后的值填回原始数组
        gradient_magnitude[mask] = normalized_values
    
    return gradient_magnitude

def find_mask_contours(mask):
    """找到mask的轮廓"""
    mask_uint8 = mask.astype(np.uint8) * 255
    contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:  # 如果没有找到轮廓
        return [], (0, 0)
    
    # 找到最大的轮廓
    max_contour = max(contours, key=cv2.contourArea)
    
    # 计算最大轮廓的几何中心
    M = cv2.moments(max_contour)
    if M["m00"] > 0:  # 确保轮廓的面积大于0
        cx = int(M["m10"] / M["m00"])
        cy = int(M["m01"] / M["m00"])
        center = (cx, cy)
    else:
        center = (0, 0)
    
    return contours, center

def find_stable_region(mask, gradient, target_ratio=0.2):
    """找到法线变化最小的连通区域（占mask面积的20%）"""
    # 仅考虑mask内部的梯度
    masked_gradient = gradient.copy()
    masked_gradient[mask == 0] = float('inf')  # 将mask外部的梯度设为无穷大
    
    # 计算目标面积
    mask_area = np.sum(mask)
    target_area = int(mask_area * target_ratio)
    
    # 基于梯度值排序
    flat_indices = np.argsort(masked_gradient.flatten())
    flat_mask = mask.flatten()
    valid_indices = [idx for idx in flat_indices if flat_mask[idx] > 0]
    
    # 选择最低梯度的点，直到达到目标面积
    stability_mask = np.zeros_like(mask, dtype=bool)
    h, w = mask.shape
    count = 0
    
    for idx in valid_indices:
        if count >= target_area:
            break
        y, x = idx // w, idx % w
        stability_mask[y, x] = True
        count += 1
    
    # 进行连通区域分析
    labeled_array, num_features = label(stability_mask)
    
    # 选择最大的连通区域
    if num_features > 0:
        sizes = np.bincount(labeled_array.flatten())[1:]  # 跳过背景
        largest_label = np.argmax(sizes) + 1  # +1 因为背景是0
        stable_region = (labeled_array == largest_label)
    else:
        stable_region = np.zeros_like(mask, dtype=bool)
    
    return stable_region

def calculate_safe_point0(mask, gradient):
    """计算安全坐标"""
    # 创建距离变换
    dist_transform = distance_transform_edt(mask)
    
    # 结合距离变换和梯度的倒数创建安全度量
    # 我们希望远离边缘且梯度小的地方
    gradient_inv = 1.0 - gradient.copy()
    gradient_inv[mask == 0] = 0  # 只考虑mask内部
    
    safety_metric = dist_transform * gradient_inv
    
    # 找到安全度量最大的点
    y, x = np.unravel_index(np.argmax(safety_metric), safety_metric.shape)
    return (x, y)

def calculate_safe_point(mask, gradient, high_ratio=0.1):
    """计算安全坐标，主要考虑法向变化速率大的区域
    
    Args:
        mask: 区域掩码，形状为(H, W)
        gradient: 法向变化速率，形状为(H, W)
        high_ratio: 高变化率区域占mask总面积的比例，默认0.1（即10%）
    
    Returns:
        安全坐标 (x, y)
    """
    # 确保mask是布尔类型
    mask = mask.astype(bool)
    
    # 如果mask为空，返回(0,0)
    if not np.any(mask):
        return (0, 0)
    
    # 提取mask区域内的梯度值
    mask_gradient_values = gradient[mask]
    
    # 如果所有值都相同，使用原始方法
    if np.max(mask_gradient_values) == np.min(mask_gradient_values):
        dist_transform = distance_transform_edt(mask)
        y, x = np.unravel_index(np.argmax(dist_transform), dist_transform.shape)
        return (x, y)
    
    # 自动计算阈值，使高于阈值的区域占比为high_ratio
    # 使用percentile，这样的阈值会取第(1-high_ratio)*100百分位
    threshold = np.percentile(mask_gradient_values, (1 - high_ratio) * 100)
    
    # 创建高变化率掩码
    high_gradient_mask = gradient > threshold
    high_gradient_mask = high_gradient_mask & mask  # 只在mask内考虑
    
    # 如果高变化率区域为空（理论上不应该发生，因为我们已经计算了percentile）
    if not np.any(high_gradient_mask):
        # 使用原始方法作为备选
        dist_transform = distance_transform_edt(mask)
        gradient_inv = 1.0 - gradient.copy()
        gradient_inv[mask == 0] = 0
        safety_metric = dist_transform * gradient_inv
        y, x = np.unravel_index(np.argmax(safety_metric), safety_metric.shape)
        return (x, y)
    
    # 计算到高变化率区域的距离
    distance_to_high_gradient = distance_transform_edt(~high_gradient_mask)
    
    # 只考虑mask内的距离
    distance_to_high_gradient[~mask] = 0
    
    # 找到距离高变化率区域最远的点
    y, x = np.unravel_index(np.argmax(distance_to_high_gradient), distance_to_high_gradient.shape)
    return (x, y)

def calculate_safe_point2(mask, gradient, high_ratio=0.1):
    """计算安全坐标，仅考虑远离法向变化速率大的区域
    
    Args:
        mask: 区域掩码，形状为(H, W)
        gradient: 法向变化速率，形状为(H, W)
        high_ratio: 高变化率区域占mask总面积的比例，默认0.1（即10%）
    
    Returns:
        安全坐标 (x, y)
    """
    # 确保mask是布尔类型
    mask = mask.astype(bool)
    
    # 如果mask为空，返回(0,0)
    if not np.any(mask):
        return (0, 0)
    
    # 提取mask区域内的梯度值
    mask_gradient_values = gradient[mask]
    
    # 如果所有值都相同，选择mask质心作为安全点
    if np.max(mask_gradient_values) == np.min(mask_gradient_values):
        # 计算质心
        M = cv2.moments(mask.astype(np.uint8))
        if M["m00"] > 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            return (cx, cy)
        else:
            # 如果无法计算质心，返回mask中的第一个点
            y, x = np.where(mask)
            if len(y) > 0:
                return (x[0], y[0])
            return (0, 0)
    
    # 确定高梯度阈值 - 选择最高的high_ratio比例的梯度值
    threshold = np.percentile(mask_gradient_values, (1 - high_ratio) * 100)
    
    # 创建高变化率掩码
    high_gradient_mask = (gradient > threshold) & mask
    
    # 如果高变化率区域为空（理论上不应该发生）
    if not np.any(high_gradient_mask):
        # 使用质心作为备选
        M = cv2.moments(mask.astype(np.uint8))
        if M["m00"] > 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            return (cx, cy)
        else:
            y, x = np.where(mask)
            if len(y) > 0:
                return (x[0], y[0])
            return (0, 0)
    
    # 计算到高梯度区域的距离变换
    # 我们关注的是mask内部到高梯度区域的距离
    distance_to_high_gradient = distance_transform_edt(~high_gradient_mask)
    
    # 只考虑mask内部区域
    distance_to_high_gradient[~mask] = 0
    
    # 找到距离高梯度区域最远的点
    y, x = np.unravel_index(np.argmax(distance_to_high_gradient), distance_to_high_gradient.shape)
    
    return (x, y)


# 法线图的箭头可视化
def normal_vis_arrow(image, normals, arrowPath):
    # 从原图中稀疏采样50x50个点，并计算相应位置处法线的终点坐标，记录在列表中
    points = []
    scale = 30
    height, width = image.shape[:2]
    step_h = height // 40  # 在高度方向采样50个点
    step_w = width // 40   # 在宽度方向采样50个点
    for i in range(0, height, step_h):
        for j in range(0, width, step_w):
            # 从法线图中获取法线
            normal = normals[i, j]
            # 计算法线的终点坐标
            x = j
            y = i
            z = 0
            length = np.linalg.norm(normal)
            normal = normal / length
            x_end = x + normal[0] * scale
            y_end = y + normal[1] * scale * (-1)
            z_end = z + normal[2] * scale
            points.append([(x, y, z), (x_end, y_end, z_end)])

    # 使cv2的方法，根据points中的坐标把所有的箭头绘制在原图上，箭头本身为红色，起点为绿色，终点为黄色
    for point in points:
        # 箭头起点
        cv2.circle(image, (int(point[0][0]), int(point[0][1])), 1, (0, 255, 0), -1)
        # # 箭头终点
        # cv2.circle(image, (int(point[1][0]), int(point[1][1])), 1, (0, 255, 255), -1)
        # 箭头
        cv2.arrowedLine(image, (int(point[0][0]), int(point[0][1])), (int(point[1][0]), int(point[1][1])), (0, 0, 255), 1)
    # 将图保存到本地
    cv2.imwrite(arrowPath, image)

def find_mask_diameter(mask, sample_ratio=0.5):
    """
    寻找经过mask几何中心且长度最大的直径
    
    Args:
        mask: 2D布尔数组，表示mask区域
    
    Returns:
        start_point: 直径的起点坐标 (x, y)
        end_point: 直径的终点坐标 (x, y)
    """
    # 确保mask是布尔类型
    mask = mask.astype(np.uint8)
    
    # 计算mask的几何中心（质心）
    M = cv2.moments(mask)
    if M["m00"] == 0:
        return None, None  # 空mask
    
    center_x = int(M["m10"] / M["m00"])
    center_y = int(M["m01"] / M["m00"])
    center = (center_x, center_y)
    
    # 找到mask的轮廓
    contours, _ = find_mask_contours(mask)
    if not contours:
        return None, None
    
    # 合并所有轮廓点并按固定间隔采样
    all_contour_points = []
    for contour in contours:
        # 计算采样步长，确保至少取2个点
        step = max(1, int(1 / sample_ratio))
        sampled_points = [point[0] for point in contour[::step]]
        all_contour_points.extend(sampled_points)
    
    # 如果轮廓点太少，返回None
    if len(all_contour_points) < 2:
        return None, None
    
    # 寻找经过或接近中心的最长线段
    max_distance = 0
    best_pair = None
    
    # 设置接近中心的阈值（线到中心点的最大距离）
    threshold = max(5, np.sqrt(mask.shape[0]**2 + mask.shape[1]**2) * 0.05)  # 5或图像对角线的5%
    
    # 对每一对轮廓点检查
    n = len(all_contour_points)
    for i in range(n):
        for j in range(i+1, n):
            p1 = all_contour_points[i]
            p2 = all_contour_points[j]
            
            # 计算点到线的距离
            # 线段方程: (x-x1)/(x2-x1) = (y-y1)/(y2-y1)
            # 距离公式: |Ax + By + C|/sqrt(A^2 + B^2)，其中线的方程为Ax + By + C = 0
            
            if p1[0] == p2[0]:  # 垂直线
                distance_to_center = abs(center_x - p1[0])
            elif p1[1] == p2[1]:  # 水平线
                distance_to_center = abs(center_y - p1[1])
            else:
                # 一般情况
                A = (p2[1] - p1[1])
                B = (p1[0] - p2[0])
                C = (p2[0]*p1[1] - p1[0]*p2[1])
                distance_to_center = abs(A*center_x + B*center_y + C) / np.sqrt(A**2 + B**2)
            
            # 如果线段经过或接近中心，计算长度
            if distance_to_center <= threshold:
                # 计算线段长度
                distance = np.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
                
                if distance > max_distance:
                    max_distance = distance
                    best_pair = (p1, p2)
    
    if best_pair is None:
        return None, None
    
    return best_pair[0], best_pair[1], center

def visualize_results(image, masks, normal_map, output_path="result.jpg"):
    """可视化处理结果"""
    
    # 创建结果图像
    result_image = image.copy()
    
    # 创建热力图颜色映射
    colormap = matplotlib.colormaps.get_cmap('jet')

    # 用于存储所有mask内的法向变化速率值
    all_gradient_values = []
    
    # 对每个检测到的实例进行处理
    for i, mask_tensor in enumerate(masks):
        # 转换为numpy数组
        mask = mask_tensor.cpu().numpy()
        
        # # 计算mask边界并用红色可视化：可行
        # contours = find_mask_contours(mask)
        # cv2.drawContours(result_image, contours, -1, (255, 0, 0), 2)
        
        # 计算此mask区域内的法线变化速率
        mask_gradient = calculate_normal_gradient(normal_map, mask)
        
        # 收集所有有效的梯度值（用于统计分布）
        valid_gradients = mask_gradient[mask > 0]
        all_gradient_values.extend(valid_gradients.flatten())

        # 可视化增强，提高区分度 - 使用伽马校正或对数变换
        # 选择1：伽马校正（增强对比度）
        gamma = 0.4  # 小于1的值会增强低值区域的对比度
        enhanced_gradient = np.power(mask_gradient, gamma)

        
        # 创建热力图
        # 首先创建与图像大小一致的全零数组
        heatmap = np.zeros_like(image)
        # 只处理mask区域
        mask_indices = np.where(mask)
        # 获取mask区域内的梯度值-增强可视化版
        mask_values = enhanced_gradient[mask_indices]
        # 应用colormap并转换为RGB
        # 将值映射到[0, 1]的范围
        normalized_values = mask_values.flatten()
        # 使用matplotlib的colormap
        colors = colormap(normalized_values)
        # 从RGBA转换为RGB并缩放到[0, 255]
        colors_rgb = (colors[:, :3] * 255).astype(np.uint8)
        # 将颜色填回到heatmap中
        for idx, (y, x) in enumerate(zip(*mask_indices)):
            heatmap[y, x] = colors_rgb[idx]
        
        # 将热力图应用到结果图像
        alpha = 0.5  # 热力图透明度
        mask_3d = np.repeat(mask[:, :, np.newaxis], 3, axis=2)
        result_image = np.where(
            mask_3d > 0,
            cv2.addWeighted(result_image, 1-alpha, heatmap, alpha, 0),
            result_image
        )
        
        # 找到法线变化最小的连通区域（占面积20%）
        stable_region = find_stable_region(mask, mask_gradient)
        stable_contours, safe_point = find_mask_contours(stable_region)  # 用连通区域的几何中心作为采摘点
        cv2.drawContours(result_image, stable_contours, -1, (0, 0, 0), 1)
        
        # # 计算安全坐标并标记
        # safe_point = calculate_safe_point(mask, mask_gradient)

        cv2.circle(result_image, safe_point, 3, (255, 255, 255), -1)  # 白色实心圆

        # 找到经过几何中心的最长直径
        start_point, end_point, center = find_mask_diameter(mask)
        if start_point is not None and end_point is not None:
            # 顺便把几何中心绘制出来
            cv2.circle(result_image, center, 3, (255, 0, 0), -1)  # 红色实心点
            # 绘制直径线段（白色，粗细为2）
            cv2.line(result_image, start_point, end_point, (255, 255, 255), 1)
            # # 在直径端点绘制小圆点（可选）
            # cv2.circle(result_image, start_point, 3, (0, 255, 255), -1)  # 黄色
            # cv2.circle(result_image, end_point, 3, (0, 255, 255), -1)  # 黄色
    
    # 保存结果图像
    cv2.imwrite(output_path, cv2.cvtColor(result_image, cv2.COLOR_RGB2BGR))
    print('-----Visual conbined done')

    # 统计法向梯度和像素数量的关系
    # 如果收集到了梯度值，绘制分布图
    if all_gradient_values and 0:
        # 提取文件名前缀，用于保存分布图
        prefix = output_path.rsplit('.', 1)[0]
        
        # 创建分布图
        plt.figure(figsize=(10, 6))
        
        # 绘制直方图
        n, bins, patches = plt.hist(all_gradient_values, bins=50, alpha=0.7, color='blue')
        
        # 绘制核密度估计曲线（可选）
        from scipy.stats import gaussian_kde
        density = gaussian_kde(all_gradient_values)
        x_range = np.linspace(min(all_gradient_values), max(all_gradient_values), 200)
        plt.plot(x_range, density(x_range) * len(all_gradient_values) * (bins[1] - bins[0]), 'r-', linewidth=2)
        
        # 添加标题和标签
        plt.title('Distribution of Normal Direction Variation Rates')
        plt.xlabel('Variation Rate')
        plt.ylabel('Pixel Count')
        plt.grid(True, alpha=0.3)
        
        # 保存分布图
        distribution_path = f"{prefix}_distribution.png"
        plt.savefig(distribution_path)
        plt.close()
        
        # 计算并打印一些统计信息
        mean_value = np.mean(all_gradient_values)
        median_value = np.median(all_gradient_values)
        std_value = np.std(all_gradient_values)
        print(f"-----Normal variation statistics:")
        print(f"  Mean: {mean_value:.4f}")
        print(f"  Median: {median_value:.4f}")
        print(f"  Standard deviation: {std_value:.4f}")
    
    return result_image

# 指定mask以测试
def manual_mask(image, original_masks):
    # 创建手动矩形mask
    # 获取图像尺寸
    img_height, img_width = image.shape[:2]
    
    # 创建全零mask
    # 确保与YOLO输出格式一致（通常是torch.Tensor）
    if len(original_masks) > 0:
        # 使用与原始masks相同的设备和数据类型
        device = original_masks.device
        dtype = original_masks.dtype
    else:
        # 默认设置
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        dtype = torch.bool
    
    # 创建一个只包含一个mask的张量
    manual_mask = torch.zeros((1, img_height, img_width), dtype=dtype, device=device)
    
    # 定义矩形区域（居中）
    rect_width = img_width // 4   # 矩形宽度为图像宽度的1/3
    rect_height = img_height // 4  # 矩形高度为图像高度的1/3
    
    # 计算矩形的左上角和右下角坐标
    left = (img_width - rect_width) // 2
    top = (img_height - rect_height) // 2
    right = left + rect_width
    bottom = top + rect_height
    
    # 填充矩形区域
    manual_mask[0, top:bottom, left:right] = 1
    
    # 用手动创建的mask替换原始masks
    print('-----masks replaced')
    return manual_mask

def process_mushroom_image(image_path, yolo_model_path, combine_output_path="result.jpg"):
    """主处理函数"""
    # 提取原图文件名（不带扩展名）
    base_name = image_path.split('/')[-1].split('.')[0]
    
    # 加载图像
    image = cv2.imread(image_path)
    # 获取原图的尺寸
    height, width = image.shape[:2]
    # 计算缩放比例
    max_size = 840
    if max(height, width) > max_size:
        scale = max_size / max(height, width)
        new_height = int(height * scale)
        new_width = int(width * scale)
        # 按照比例缩放图像至840以下
        image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
    else:
        new_height = height
        new_width = width
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # 加载模型
    yolo_model, pipe_normal = load_models(yolo_model_path)
    
    # YOLO分割
    results = yolo_model(image_rgb, conf=0.7, max_det=130)[0]  # image_path
    masks = results.masks.data if results.masks is not None else []
    # # 手动赋值mask以测试
    # masks = manual_mask(image_rgb, masks)  
    print('-----YOLO segment done')

    # 如果没有检测到蘑菇
    if len(masks) == 0:
        print("未检测到蘑菇实例")
        return image_rgb
    
    # Marigold模型法线估计
    marigold_image = diffusers.utils.load_image(image_path).resize((new_width, new_height))  # 注意缩放尺寸
    normals = pipe_normal(marigold_image).prediction
    print('-----Diffusion normals done')

    # # 法线图片可视化
    # vis_normal = pipe_normal.image_processor.visualize_normals(normals)
    # # 生成法线图和综合可视化图的文件名
    # normals_output_path = f"dev-utils/results/{base_name}-normals.png"
    # vis_normal[0].save(normals_output_path)
    
    # 将normals转换为numpy数组并调整大小以匹配原图
    normals_np = np.array(normals[0])
    
    # # 箭头可视化
    # arrowPath = f"dev-utils/results/{base_name}-arrow.png"
    # normal_vis_arrow(image, normals_np, arrowPath)

    normals_resized = cv2.resize(normals_np, (image.shape[1], image.shape[0]))
    
    # 可视化结果
    combine_output_path = f"dev-utils/results/{base_name}-combine20-10.png"
    result_image = visualize_results(image_rgb, masks, normals_resized, combine_output_path)
    
    return result_image

if __name__ == "__main__":
    # 可以通过命令行参数或直接在代码中设置路径
    image_path = "Datasets/mushroom_dev/成簇蘑菇1_20211221145326_color.jpg"  # 'mycionics装备褐菇.png' '2_20210615123635_color.jpg'
    yolo_model_path = "yolov8-1/runs/segment/train30/weights/best.pt"
    # output_path = "dev-utils/results/centerFind_combined3635-2.jpg"
    
    result = process_mushroom_image(image_path, yolo_model_path)
    
    # # 显示结果（可选）
    # plt.figure(figsize=(12, 10))
    # plt.imshow(result)
    # plt.axis('off')
    # plt.show()